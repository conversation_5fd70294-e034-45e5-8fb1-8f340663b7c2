<template>
  <div>
    <NuxtPwaManifest />
    <n-config-provider
      :locale="locale"
      inline-theme-disabled
      :date-locale="dateLocale"
      :theme-overrides="themeOverrides"
    >
      <n-message-provider>
        <NuxtLayout name="mobile" v-if="isMobileView">
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
        <NuxtLayout name="default" v-else>
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { NConfigProvider, type GlobalThemeOverrides } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const { isMobile } = useDevice();
const route = useRoute();
const nuxtApp = useNuxtApp();
const isMobileView = ref(false);
const isFullscreen = ref(false);
const initialDeviceType = ref(isMobile);
const pageData = reactive(<any>{});

// 首次加载时使用 useDevice 判断设备类型
isMobileView.value = isMobile;

const locale = computed(() => useAuthStore().customLocal);
const dateLocale = computed(() => useAuthStore().customLocalDate);
const pageTheme = computed(() => useConfigStore().getPageTheme);

// 检测是否处于全屏状态
function checkFullscreen() {
  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
}

// 监听全屏变化
function handleFullscreenChange() {
  isFullscreen.value = checkFullscreen();
}

// 初始化数据
if (process.server) {
  const res: any = await useGetNuxtConfig({
    requestUri: route.fullPath,
    abtestPage: "", //AB测试代码
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res.data);
    const config = useRuntimeConfig();
    config.public.defaultCountryCode = pageData?.defaultCountryCode || "";
    nuxtApp.$setResponseHeaders(pageData.responseHeaders);
    let globalData = nuxtApp.$getGlobalData();
    globalData.visitCode = pageData.visitCode;
    if (pageData.headFirstScript) {
      useHead({
        script: [{ innerHTML: pageData.headFirstScript }],
      });
    }
    if (pageData.headLastScript) {
      useHead({
        script: [{ innerHTML: pageData.headLastScript }],
      });
    }
  }
  await redirectToURL();
  if (isMobileView.value) {
    useHead({
      script: [
        {
          src: "/scripts/resetFontSize.js?20250220",
          async: false,
        },
      ],
    });
  }
}

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
};

function resetFontSize() {
  const width = Math.max(document.documentElement.clientWidth, 300);
  // 如果是全屏状态，保持当前视图类型不变
  if (!isFullscreen.value) {
    const isMobilePage = width < 600;
    if (isMobileView.value !== isMobilePage) {
      // 只在非全屏状态下更新视图类型
      isMobileView.value = isMobilePage;
      redirectToURL();
    }
  }
  const fontSize = width >= 600 ? 16 : (80 * width) / 600;
  document.documentElement.style.fontSize = `${fontSize}px`;
}
const debouncedResetFontSize = debounce(resetFontSize, 50);

onMounted(() => {
  // 保存初始设备类型
  initialDeviceType.value = isMobile;

  // 添加全屏变化监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  window.addEventListener("resize", debouncedResetFontSize);
  window.addEventListener("orientationchange", resetFontSize);
  window?.MyStat?.addPageEvent(`system_vue_mounted`, "开始响应用户操作");
  resetWhatsAppClickState();
});

onUnmounted(() => {
  // 移除全屏变化监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener(
    "webkitfullscreenchange",
    handleFullscreenChange
  );
  document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
  document.removeEventListener("MSFullscreenChange", handleFullscreenChange);

  window.removeEventListener("resize", debouncedResetFontSize);
  window.removeEventListener("orientationchange", resetFontSize);
});

/**
 * 页面重定向
 * 增加全屏状态判断，全屏时不进行重定向
 */
function redirectToURL() {
  // 如果处于全屏状态，不进行重定向
  if (isFullscreen.value) {
    return;
  }

  // 如果是移动设备，优先使用初始设备类型判断
  if (
    initialDeviceType.value &&
    isMobileView.value !== initialDeviceType.value
  ) {
    isMobileView.value = initialDeviceType.value;
  }

  const currentURL = normalizePath(route.path);

  // 特殊路径处理：/contacto 和 /h5/contacto 跳转首页
  if (currentURL === "/contacto" || currentURL === "/h5/contacto") {
    if (isMobileView.value) {
      return navigateTo({
        path: "/h5/",
        hash: route.hash,
      });
    } else {
      return navigateTo({
        path: "/",
        hash: route.hash,
      });
    }
  }

  // 首页重定向
  if (isMobileView.value && currentURL === "/") {
    return navigateTo({
      path: `/h5${route.fullPath}`,
      hash: route.hash,
    });
  }
  if (!isMobileView.value && currentURL === "/h5") {
    return navigateTo({
      path: "/",
      query: route.query,
      hash: route.hash,
    });
  }

  // 其他页面基于规则自动转换
  let newPath = "";
  if (isMobileView.value) {
    // PC 端 -> 移动端
    if (!currentURL.startsWith("/h5")) {
      newPath = `/h5${currentURL}`;
    }
  } else {
    // 移动端 -> PC 端
    if (currentURL.startsWith("/h5")) {
      newPath = currentURL.replace(/^\/h5/, "");
    }
  }

  if (newPath && newPath !== currentURL) {
    return navigateTo({
      path: newPath,
      query: route.query,
      hash: route.hash,
    });
  }
}

const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: "#E50113",
  },
  Button: {
    colorPrimary: "#E50113",
    colorHoverPrimary: "#E50113",
    colorPressedPrimary: "#E50113",
    colorFocusPrimary: "#E50113",
    colorDisabledPrimary: "#E50113",
    textColorDisabledPrimary: "#E50113",
    textColorTextPrimary: "#E50113",
    textColorTextHoverPrimary: "#E50113",
    textColorTextFocusPrimary: "#E50113",
    textColorTextDisabledPrimary: "#E50113",
    textColorGhostHoverPrimary: "#E50113",
    textColorGhostPressedPrimary: "#E50113",
    borderHoverPrimary: "#E50113",
    borderPressedPrimary: "#E50113",
    borderFocusPrimary: "#E50113",
    rippleColorPrimary: "#E50113",
    textColorTextHover: "#E50113",
  },
};
</script>
