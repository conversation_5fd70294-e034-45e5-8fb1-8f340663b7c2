<template>
  <n-modal
    preset="dialog"
    :auto-focus="false"
    :block-scroll="true"
    :closable="false"
    :show-icon="false"
    v-model:show="pageData.showVideo"
    :on-close="onCloseVideo"
    :on-esc="onCloseVideo"
    :on-mask-click="onCloseVideo"
    class="home-video-dialog"
    :style="
      isMobile
        ? { width: '100%', padding: '0 0.24rem', minHeight: '5.2rem' }
        : {
            width: '800px',
            minWidth: '800px',
            padding: '0 20px',
            minHeight: '520px',
          }
    "
  >
    <div class="flex items-center justify-between">
      <div
        class="text-[#222] text-[20px] break-all whitespace-nowrap overflow-hidden text-ellipsis"
        :class="
          isMobile
            ? 'h-[0.8rem]  mr-[0.2rem]'
            : 'leading-[70px] font-bold h-[70px] mr-[50px]'
        "
      >
        <!-- {{ pageData.videoInfo?.title }} -->
      </div>
      <icon-card
        size="28"
        color="#666"
        class="mr-[-4px] cursor-pointer"
        name="ic:round-close"
        @click="onCloseVideo"
      >
      </icon-card>
    </div>
    <you-tube-player
      :width="isMobile ? 6.4 * resetFontSize : 760"
      :height="isMobile ? 3.6 * resetFontSize : 427.5"
      :youtubeId="pageData.videoInfo?.id"
      :titleCh="pageData.videoInfo?.titleCh"
      :title="pageData.videoInfo?.title"
      :autoplay="true"
    ></you-tube-player>
  </n-modal>
</template>

<script setup lang="ts" name="videoModal">
const route = useRoute();
const pageData = reactive(<any>{
  showVideo: false,
  videoInfo: <any>{},
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

function onOpenVideo(video: any) {
  pageData.videoInfo = video;
  pageData.showVideo = true;
}

function onCloseVideo() {
  pageData.showVideo = false;
}

defineExpose({
  onOpenVideo,
});
</script>
<style>
.home-video-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
