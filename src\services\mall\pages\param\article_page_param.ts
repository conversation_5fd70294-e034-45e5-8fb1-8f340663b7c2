/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { PageParam } from "../../../common";
import { VisitDeviceType, visitDeviceTypeFromJSON, visitDeviceTypeToJSON } from "../../../common/business";

export const protobufPackage = "mall.pages";

export interface ListArticleCategoryParam {
  ids: string[];
  names: string[];
}

export interface ListArticleParam {
  deviceType: VisitDeviceType;
  articleCategoryId: string;
}

export interface ArticleDetailParam {
  id: string;
  /** 文章标题 */
  title: string;
  /** 文章代码 */
  articleCode: string;
}

/** 查询BLOG列表的参数 */
export interface GetBlogListParam {
  page: PageParam | undefined;
  deviceType: VisitDeviceType;
}

export interface SearchArticleListParam {
  page:
    | PageParam
    | undefined;
  /** 搜索关键词 */
  keyword: string;
  /** 发布日期 */
  publishDate: string;
  /** 子类目ID(标签) */
  cateId: string;
}

function createBaseListArticleCategoryParam(): ListArticleCategoryParam {
  return { ids: [], names: [] };
}

export const ListArticleCategoryParam = {
  encode(message: ListArticleCategoryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.ids) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.names) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ListArticleCategoryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListArticleCategoryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ids.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.names.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListArticleCategoryParam {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.String(e)) : [],
      names: globalThis.Array.isArray(object?.names) ? object.names.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: ListArticleCategoryParam): unknown {
    const obj: any = {};
    if (message.ids?.length) {
      obj.ids = message.ids;
    }
    if (message.names?.length) {
      obj.names = message.names;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListArticleCategoryParam>, I>>(base?: I): ListArticleCategoryParam {
    return ListArticleCategoryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListArticleCategoryParam>, I>>(object: I): ListArticleCategoryParam {
    const message = createBaseListArticleCategoryParam();
    message.ids = object.ids?.map((e) => e) || [];
    message.names = object.names?.map((e) => e) || [];
    return message;
  },
};

function createBaseListArticleParam(): ListArticleParam {
  return { deviceType: 0, articleCategoryId: "" };
}

export const ListArticleParam = {
  encode(message: ListArticleParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.deviceType !== 0) {
      writer.uint32(8).int32(message.deviceType);
    }
    if (message.articleCategoryId !== "") {
      writer.uint32(18).string(message.articleCategoryId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ListArticleParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListArticleParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.articleCategoryId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListArticleParam {
    return {
      deviceType: isSet(object.deviceType) ? visitDeviceTypeFromJSON(object.deviceType) : 0,
      articleCategoryId: isSet(object.articleCategoryId) ? globalThis.String(object.articleCategoryId) : "",
    };
  },

  toJSON(message: ListArticleParam): unknown {
    const obj: any = {};
    if (message.deviceType !== 0) {
      obj.deviceType = visitDeviceTypeToJSON(message.deviceType);
    }
    if (message.articleCategoryId !== "") {
      obj.articleCategoryId = message.articleCategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListArticleParam>, I>>(base?: I): ListArticleParam {
    return ListArticleParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListArticleParam>, I>>(object: I): ListArticleParam {
    const message = createBaseListArticleParam();
    message.deviceType = object.deviceType ?? 0;
    message.articleCategoryId = object.articleCategoryId ?? "";
    return message;
  },
};

function createBaseArticleDetailParam(): ArticleDetailParam {
  return { id: "", title: "", articleCode: "" };
}

export const ArticleDetailParam = {
  encode(message: ArticleDetailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.articleCode !== "") {
      writer.uint32(26).string(message.articleCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticleDetailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticleDetailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.articleCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticleDetailParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      articleCode: isSet(object.articleCode) ? globalThis.String(object.articleCode) : "",
    };
  },

  toJSON(message: ArticleDetailParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.articleCode !== "") {
      obj.articleCode = message.articleCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticleDetailParam>, I>>(base?: I): ArticleDetailParam {
    return ArticleDetailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticleDetailParam>, I>>(object: I): ArticleDetailParam {
    const message = createBaseArticleDetailParam();
    message.id = object.id ?? "";
    message.title = object.title ?? "";
    message.articleCode = object.articleCode ?? "";
    return message;
  },
};

function createBaseGetBlogListParam(): GetBlogListParam {
  return { page: undefined, deviceType: 0 };
}

export const GetBlogListParam = {
  encode(message: GetBlogListParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    if (message.deviceType !== 0) {
      writer.uint32(160).int32(message.deviceType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetBlogListParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlogListParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBlogListParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      deviceType: isSet(object.deviceType) ? visitDeviceTypeFromJSON(object.deviceType) : 0,
    };
  },

  toJSON(message: GetBlogListParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.deviceType !== 0) {
      obj.deviceType = visitDeviceTypeToJSON(message.deviceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBlogListParam>, I>>(base?: I): GetBlogListParam {
    return GetBlogListParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlogListParam>, I>>(object: I): GetBlogListParam {
    const message = createBaseGetBlogListParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.deviceType = object.deviceType ?? 0;
    return message;
  },
};

function createBaseSearchArticleListParam(): SearchArticleListParam {
  return { page: undefined, keyword: "", publishDate: "", cateId: "" };
}

export const SearchArticleListParam = {
  encode(message: SearchArticleListParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    if (message.keyword !== "") {
      writer.uint32(162).string(message.keyword);
    }
    if (message.publishDate !== "") {
      writer.uint32(242).string(message.publishDate);
    }
    if (message.cateId !== "") {
      writer.uint32(322).string(message.cateId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SearchArticleListParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchArticleListParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.keyword = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.publishDate = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.cateId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchArticleListParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      publishDate: isSet(object.publishDate) ? globalThis.String(object.publishDate) : "",
      cateId: isSet(object.cateId) ? globalThis.String(object.cateId) : "",
    };
  },

  toJSON(message: SearchArticleListParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.publishDate !== "") {
      obj.publishDate = message.publishDate;
    }
    if (message.cateId !== "") {
      obj.cateId = message.cateId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchArticleListParam>, I>>(base?: I): SearchArticleListParam {
    return SearchArticleListParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchArticleListParam>, I>>(object: I): SearchArticleListParam {
    const message = createBaseSearchArticleListParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.keyword = object.keyword ?? "";
    message.publishDate = object.publishDate ?? "";
    message.cateId = object.cateId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
