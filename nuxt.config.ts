// import Components from "unplugin-vue-components/vite";
// import { NaiveUiResolver } from "unplugin-vue-components/resolvers";

import { visualizer } from "rollup-plugin-visualizer";
import compression from "vite-plugin-compression";
import viteImagemin from "vite-plugin-imagemin";
import { getFullRouteRules } from "./src/utils/redirectRules";

const sw = process.env.SW === "true";

export default defineNuxtConfig({
  srcDir: "src/",
  modules: [
    "@bg-dev/nuxt-naiveui",
    "@pinia/nuxt",
    "@nuxtjs/device",
    "@vueuse/nuxt",
    "@unocss/nuxt",
    "nuxt-icon",
    "nuxt-lodash",
    "@vite-pwa/nuxt",
  ],
  devtools: { enabled: true },
  css: ["assets/styles/global.css", "@unocss/reset/tailwind-compat.css"],
  runtimeConfig: {
    apiSecret: "123",
    public: {
      baseURL: process.env.NUXT_PUBLIC_API_BASE,
      clientURL: process.env.NUXT_PUBLIC_API_CLIENT,
    },
    userInfo: {},
    abtestMode: "", //首页A/B测试模式
    defaultCountryCode: "", //基于IP地址定位所属国家
  },
  vite: {
    build: {
      target: ["safari13"],
      sourcemap: false,
      minify: "esbuild",
      chunkSizeWarningLimit: 500,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes("node_modules")) {
              return "vendor";
            }
          },
        },
      },
    },
    plugins: [
      // 性能报告
      // visualizer({
      //   open: true,
      //   gzipSize: true,
      // }),
      // gzip压缩
      compression({
        algorithm: "gzip",
        ext: ".gz",
        filter: /\.(js|mjs|json|css|html|ttf|otf|woff|woff2)$/i,
      }),
      // 图片压缩
      viteImagemin({
        optipng: {
          optimizationLevel: 7,
        },
        pngquant: {
          quality: [0.8, 0.9],
        },
        svgo: {
          plugins: [
            {
              name: "removeViewBox",
            },
            {
              name: "removeEmptyAttrs",
              active: false,
            },
          ],
        },
      }),
    ],
  },
  app: {
    head: {
      script: [
        {
          type: "text/javascript",
          src: "https://webapi.amap.com/maps?v=1.4.15&key=9448d5f183a838b2f2e294dd189235a8&plugin=AMap.ToolBar,AMap.Scale&language=zh_en",
        },
      ],
    },
  },
  nitro: {
    // 用于客户端代理
    devProxy: {
      "/api": {
        target: "http://chilat.mall.dev231.xpormayor.com.mx/api", // 这里是接口地址
        changeOrigin: true,
        prependPath: true,
      },
    },
    routeRules: getFullRouteRules(),
  },
  experimental: {
    appManifest: false,
  },
  pwa: {
    strategies: sw ? "injectManifest" : "generateSW",
    srcDir: sw ? "service-worker" : undefined,
    filename: sw ? "sw.ts" : undefined,
    registerType: "autoUpdate",
    manifest: {
      name: "Chilat",
      short_name: "Chilat",
      description: "Añadir al escritorio para un acceso rápido y fácil",
      theme_color: "#ffffff",
      background_color: "#ffffff",
      start_url: "/?utm_source=link_shortcut_desktop",
      icons: [
        {
          src: "pwa-36x36.png",
          sizes: "36x36",
          type: "image/png",
        },
        {
          src: "pwa-48x48.png",
          sizes: "48x48",
          type: "image/png",
        },
        {
          src: "pwa-72x72.png",
          sizes: "72x72",
          type: "image/png",
        },
        {
          src: "pwa-96x96.png",
          sizes: "96x96",
          type: "image/png",
        },
        {
          src: "pwa-144x144.png",
          sizes: "144x144",
          type: "image/png",
        },
        {
          src: "pwa-192x192.png",
          sizes: "192x192",
          type: "image/png",
        },
        {
          src: "pwa-512x512.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
    },
    workbox: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
    },
    injectManifest: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
    },
    client: {
      installPrompt: true,
      periodicSyncForUpdates: 3600,
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallback: "/",
      navigateFallbackAllowlist: [/^\/$/],
      type: "module",
    },
  },
  devServer: {
    // host: "**************", //电脑的Ip
    host: "**************", //电脑的Ip
    port: 3010,
  },
});
