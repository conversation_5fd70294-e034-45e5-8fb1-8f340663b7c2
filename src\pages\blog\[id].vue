<template>
  <div class="w-[740px] mx-auto bg-white mb-[225px]">
    <!-- 面包屑导航 -->
    <div class="mt-[50px]">
      <n-breadcrumb separator=">">
        <n-breadcrumb-item>
          <icon-card
            size="24"
            color="#7F7F7F"
            class="mr-[-4px] cursor-pointer"
            name="ic:sharp-home"
            @click="onNavigateHome"
          >
          </icon-card>
        </n-breadcrumb-item>
        <n-breadcrumb-item>
          <div class="text-[#7F7F7F] text-[14px] font-400 leading-[14px]">
            <a href="/blog"> Blog </a>
          </div>
        </n-breadcrumb-item>
        <n-breadcrumb-item>
          <div class="text-[#333] text-[14px] font-500 leading-[14px]">
            {{ pageData.detail?.title }}
          </div>
        </n-breadcrumb-item>
      </n-breadcrumb>
    </div>
    <!-- 标签 -->
    <div class="flex flex-wrap gap-2 mt-[54px]" v-if="pageData.detail?.articleCategories?.length > 0">
      <div v-for="(category, index) in pageData.detail?.articleCategories" :key="index">
        <n-button
          round
          strong
          secondary
          type="tertiary"
          :bordered="false"
          @click="onCategoryClick(index)"
          class="bg-[#F2F2F2] text-[#7F7F7F] font-400 text-[12px] leading-[12px]"
        >
          {{ category.name }}
        </n-button>
      </div>
    </div>
    <!-- 标题 -->
    <div class="flex flex-wrap mt-[44px]">
      <div class="text-[#333] text-[28px] font-500 leading-[32px]">
        {{ pageData.detail?.title }}
      </div>
    </div>
    <!-- 发布日期 -->
    <div class="flex flex-wrap gap-4 mt-[34px]">
      <div class="text-[#7F7F7F] text-[14px] font-400 leading-[14px] italic cursor-pointer" @click="onPublishDateClick">
        <span>Etpublicado en </span>
        <span class="text-[#4290F7]">{{ timeFormatByZone(pageData.detail?.udate, false, false, false) }}</span>
      </div>
    </div>
    <!-- 文章内容 -->
    <div class="flex flex-wrap gap-4 mt-[44px]">
      <div v-html="pageData.detail?.content" class="text-[#333] text-[18px] font-400 leading-[27px]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

const route = useRoute();
const pageData = reactive<any>({
  detail: null,
});

await onPageData();
useHead({
  title: `${pageData.detail?.title} - Chilat`,
  meta: [
    {
      name: "description",
      content: pageData.detail?.title,
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/blog/${route.params.id}`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/blog/${route.params.id}`,
    },
  ],
});

async function onPageData() {
  const res: any = await useArticleDetail({
    id: route.params.id,
    articleCode: route.params.id,
  });
  if (res?.result?.code === 200) {
    pageData.detail = res.data;
  }
}

function onNavigateHome() {
  // useRouter().push("/");
  navigateTo(`/`);
}

function onCategoryClick(index: number) {
  const category = pageData.detail?.articleCategories[index];
  navigateToPage("/blog", { cateId: category.id }, false);
}

function onPublishDateClick() {
  navigateToPage("/blog", { date: pageData.detail?.udate }, false);
}
</script>

<style scoped lang="scss"></style>
