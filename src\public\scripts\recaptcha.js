//Google reCAPTCHA
(function (w, d) {
  var s = d.createElement("script");
  var loadFlag = "INIT";
  var tasks = [];
  function showLogo(dp) {
    var badge = d.querySelector(".grecaptcha-badge");
    if (badge) {
      if (dp) {
        dp = "block";
      } else {
        dp = "none";
      }
      badge.style.display = dp;
    }
  }
  function callCAPTCHA(action, callback) {
    showLogo(true);
    grecaptcha
      .execute("6Lfe250qAAAAANeQeBbFCtFusFAprrVVZ8_JSj8D", { action: action })
      .then(function (token) {
        callback(true, token);
        showLogo(false);
      });
  }
  s.async = true;
  s.src =
    "https://www.google.com/recaptcha/api.js?render=6Lfe250qAAAAANeQeBbFCtFusFAprrVVZ8_JSj8D";
  var z = document.getElementsByTagName("script")[0];
  z.parentNode.insertBefore(s, z);
  s.onload = function () {
    grecaptcha.ready(function () {
      showLogo(false);
      loadFlag = "YES";
      for (var task of tasks) {
        callCAPTCHA(task[0], task[1]);
      }
      tasks = [];
    });
  };
  s.onerror = function () {
    loadFlag = "NO";
    for (var task of tasks) {
      task[1](false, "JsLoadError");
    }
    tasks = [];
  };
  //action例子：submit, 回调callback的参数1：success(true表示成功获取，false表示token不可用），参数2：token
  w.loadReCAPTCHA = function (action, callback) {
    if (typeof action != "string" || typeof callback != "function") {
      console.log(
        "[getTokenReCAPTCHA] parameter type error. action: " +
          typeof action +
          ", callback: " +
          typeof callback
      );
      return;
    }
    if (loadFlag == "INIT") {
      tasks.push([action, callback]);
    } else if (loadFlag == "YES") {
      callCAPTCHA(action, callback);
    } else {
      callback(false, "");
    }
  };
})(window, document);
