<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[24px] py-[50px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-medium mt-[40px]">
        Mercados de cosméticos al por mayor de Guangzhou
      </div>
    </div>
    <div class="pb-[200px] px-[38px] flex gap-[100px]">
      <div class="flex-1">
        <div class="pt-[120px]" id="ciudad-meibo">
          <div class="text-[44px] leading-[52px] font-medium">Ciudad Meibo</div>
          <div class="text-[18px] leading-[27px] mt-[50px] flex flex-col gap-[27px]">
            <div>
              Meibo Center (en chino es meibo cheng) se encuentra en el cruce entre la autopista del aeropuerto, la
              carretera este de Guangyuan y la autopista Beierhuan. Muchos autobuses pasan por el centro y una estación
              de metro se encuentra a poca distancia. El mayor mercado mayorista profesional de productos cosméticos y
              de belleza del área Asia-Pacífico. Es uno de los 12 proyectos clave de comercio apoyados por el gobierno
              de Guangzhou. La ciudad Meibo de Guangzhou tiene un área total de 50.000 metros cuadrados para la
              exposición comercial.
            </div>
            <div>
              La ciudad Meibo de Guangzhou tiene regularmente feria de belleza y productos cosméticos en primavera y
              otoño cada año. Encontrará todos los cosméticos de marca conocidos fabricados en China y productos de
              marcas famosas de Europa, América y el sudeste asiático. La Feria de Guangzhou Meibo atrae a fabricantes y
              distribuidores, revendedores de todo el mundo. Varios tipos de cosméticos de salón de belleza y productos
              relacionados de exposiciones especiales se llevarán a cabo de forma regular. La ciudad Meibo de Guangzhou
              se ha convertido en un puente efectivo entre proveedores y usuarios.
            </div>
            <div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span> Campo comercial: productos de belleza y peluquería </span>
              </div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span> Dirección: No 121, Guangyuan W. Road, Distrito Baiyun, Guangzhou </span>
              </div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span>Número de teléfono de la oficina: (020) 61149830/61149831</span>
              </div>
            </div>
            <img
              loading="lazy"
              class="mt-[7px]"
              src="@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/canton1.png"
              alt="mercados de cosmeticos al por mayor de guangzhou"
            />
          </div>
        </div>
        <div class="pt-[80px]" id="plaza-xinfa">
          <div class="text-[44px] leading-[52px] font-medium">Plaza Xinfa</div>
          <div class="text-[18px] leading-[27px] mt-[50px] flex flex-col gap-[27px]">
            <div>
              Cubriendo más de 50.000 metros cuadrados, la plaza Xinfa tiene 1.700 oficinas y stands. Rodeado por
              Guangdong Video City, Tianlong Circuit City y la plaza Yifa, este mercado también ofrece bancos,
              almacenes, estacionamientos y estaciones de carga.
            </div>
            <div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span>
                  Campo comercial: accesorios para el cabello y la belleza, cosméticos, productos químicos y productos
                  de limpieza
                </span>
              </div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span> Dirección: No 138, Airport Road, Baiyun District, Guangzhou </span>
              </div>
              <div class="flex items-start gap-[8px] ml-[8px]">
                <span style="font-size: 30px">&middot;</span>
                <span> Número de teléfono de la oficina: 020-86551837 </span>
              </div>
            </div>

            <img
              loading="lazy"
              src="@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/canton2.png"
              alt="mercados de cosmeticos al por mayor de guangzhou"
              class="mt-[7px]"
            />
          </div>
        </div>
        <market-nav />
      </div>
      <page-anchor :anchorItems="anchorItems" />
    </div>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";

useHead({
  title: "Mercados de cosmeticos al por mayor de Guangzhou - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { text: "Ferias y mercado" },
  { link: "/canton", text: "Cantón" },
  { link: "/mercado-de-guangzhou", text: "Mercado mayorista en Guangzhou" },
  {
    link: "/mercados-de-cosmeticos-al-por-mayor-de-guangzhou",
    text: "Mercados de cosméticos al por mayor de Guangzhou",
  },
];

const anchorItems = [
  {
    title: "Ciudad Meibo",
    href: "#ciudad-meibo",
  },
  {
    title: "Plaza Xinfa",
    href: "#plaza-xinfa",
  },
];
</script>

<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100% 100%;
  background-image: url("@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/header-bg.png");
  background-repeat: no-repeat;
}
</style>
