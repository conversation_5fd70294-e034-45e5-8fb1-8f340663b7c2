syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "common/business.proto";



//解析购买的SKU文本的接口参数
message ParseBuySkuTextParam {
    string skuText = 20; //以文本方式批量添加的SKU信息（多个SKU，总是以换行符“\n”分隔）
    string headLine = 30; //头部行（即字段名行，总是用制表符“\t”分隔）
}

//预览购买的SKU列表的接口参数
message PreviewBuySkuListParam {
    repeated CheckoutSkuParam skuList = 10; //SKU列表
}

//购买的SKU信息
message CheckoutSkuParam {
    string skuId = 10; // sku id
    int32 goodsQty = 20; //下单数量
    double buyPrice = 30; //结算单价（TODO：业务员或客服改价，须检查权限）
//    string spm = 100; //SPM跟踪码
}

//计算预估运费的参数
message MidEstimateFreightParam {
    int32 siteId = 10; // 当前站点ID（即页面选中的配送国家ID；必填）
    string routeId = 20; //当前页面选中的线路（不传，则取默认值）
    bool ignoreRouteIdError = 30; //忽略线路ID错误（从购物车中取到的线路，作为默认传参时，将此参数置为true）
    map<string, int32> skuQuantityMap = 40; //sku数量Map（KEY: skuId, VALUE: quantity）
}