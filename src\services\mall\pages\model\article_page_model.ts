/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { ArticleModel } from "../../../chilat/support/model/article_category_model";
import { Page, Result } from "../../../common";
import Long from "long";

export const protobufPackage = "mall.pages";

export interface ListArticleResp {
  result: Result | undefined;
  data: ArticleModel[];
}

export interface ArticleResp {
  result: Result | undefined;
  data: ArticleModel | undefined;
}

/** 获取BLOG列表的返回 */
export interface GetBlogListResp {
  result: Result | undefined;
  page: Page | undefined;
  data: GetBlogListModel[];
}

/** 获取BLOG列表的Model */
export interface GetBlogListModel {
  /** 文章ID */
  id: string;
  /** 文章代码 */
  articleCode: string;
  /** 标题 */
  title: string;
  /** logo */
  logo: string;
  /** 作者 */
  author: string;
  /** 简介 */
  introduce: string;
  /** 创建时间 */
  cdate: number;
  /** 修改时间 */
  udate: number;
}

export interface SearchArticleListResp {
  result: Result | undefined;
  page: Page | undefined;
  data: GetBlogListModel[];
}

function createBaseListArticleResp(): ListArticleResp {
  return { result: undefined, data: [] };
}

export const ListArticleResp = {
  encode(message: ListArticleResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      ArticleModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ListArticleResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListArticleResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(ArticleModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListArticleResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => ArticleModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: ListArticleResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => ArticleModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListArticleResp>, I>>(base?: I): ListArticleResp {
    return ListArticleResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListArticleResp>, I>>(object: I): ListArticleResp {
    const message = createBaseListArticleResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => ArticleModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseArticleResp(): ArticleResp {
  return { result: undefined, data: undefined };
}

export const ArticleResp = {
  encode(message: ArticleResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      ArticleModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArticleResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArticleResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = ArticleModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArticleResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? ArticleModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: ArticleResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = ArticleModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ArticleResp>, I>>(base?: I): ArticleResp {
    return ArticleResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ArticleResp>, I>>(object: I): ArticleResp {
    const message = createBaseArticleResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? ArticleModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetBlogListResp(): GetBlogListResp {
  return { result: undefined, page: undefined, data: [] };
}

export const GetBlogListResp = {
  encode(message: GetBlogListResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      GetBlogListModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetBlogListResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlogListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(GetBlogListModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBlogListResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => GetBlogListModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: GetBlogListResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => GetBlogListModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBlogListResp>, I>>(base?: I): GetBlogListResp {
    return GetBlogListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlogListResp>, I>>(object: I): GetBlogListResp {
    const message = createBaseGetBlogListResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => GetBlogListModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetBlogListModel(): GetBlogListModel {
  return { id: "", articleCode: "", title: "", logo: "", author: "", introduce: "", cdate: 0, udate: 0 };
}

export const GetBlogListModel = {
  encode(message: GetBlogListModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.articleCode !== "") {
      writer.uint32(162).string(message.articleCode);
    }
    if (message.title !== "") {
      writer.uint32(242).string(message.title);
    }
    if (message.logo !== "") {
      writer.uint32(322).string(message.logo);
    }
    if (message.author !== "") {
      writer.uint32(402).string(message.author);
    }
    if (message.introduce !== "") {
      writer.uint32(482).string(message.introduce);
    }
    if (message.cdate !== 0) {
      writer.uint32(560).int64(message.cdate);
    }
    if (message.udate !== 0) {
      writer.uint32(640).int64(message.udate);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetBlogListModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlogListModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.articleCode = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.title = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.logo = reader.string();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.author = reader.string();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.introduce = reader.string();
          continue;
        case 70:
          if (tag !== 560) {
            break;
          }

          message.cdate = longToNumber(reader.int64() as Long);
          continue;
        case 80:
          if (tag !== 640) {
            break;
          }

          message.udate = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBlogListModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      articleCode: isSet(object.articleCode) ? globalThis.String(object.articleCode) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      logo: isSet(object.logo) ? globalThis.String(object.logo) : "",
      author: isSet(object.author) ? globalThis.String(object.author) : "",
      introduce: isSet(object.introduce) ? globalThis.String(object.introduce) : "",
      cdate: isSet(object.cdate) ? globalThis.Number(object.cdate) : 0,
      udate: isSet(object.udate) ? globalThis.Number(object.udate) : 0,
    };
  },

  toJSON(message: GetBlogListModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.articleCode !== "") {
      obj.articleCode = message.articleCode;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.logo !== "") {
      obj.logo = message.logo;
    }
    if (message.author !== "") {
      obj.author = message.author;
    }
    if (message.introduce !== "") {
      obj.introduce = message.introduce;
    }
    if (message.cdate !== 0) {
      obj.cdate = Math.round(message.cdate);
    }
    if (message.udate !== 0) {
      obj.udate = Math.round(message.udate);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBlogListModel>, I>>(base?: I): GetBlogListModel {
    return GetBlogListModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlogListModel>, I>>(object: I): GetBlogListModel {
    const message = createBaseGetBlogListModel();
    message.id = object.id ?? "";
    message.articleCode = object.articleCode ?? "";
    message.title = object.title ?? "";
    message.logo = object.logo ?? "";
    message.author = object.author ?? "";
    message.introduce = object.introduce ?? "";
    message.cdate = object.cdate ?? 0;
    message.udate = object.udate ?? 0;
    return message;
  },
};

function createBaseSearchArticleListResp(): SearchArticleListResp {
  return { result: undefined, page: undefined, data: [] };
}

export const SearchArticleListResp = {
  encode(message: SearchArticleListResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      GetBlogListModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SearchArticleListResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchArticleListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(GetBlogListModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchArticleListResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => GetBlogListModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: SearchArticleListResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => GetBlogListModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchArticleListResp>, I>>(base?: I): SearchArticleListResp {
    return SearchArticleListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchArticleListResp>, I>>(object: I): SearchArticleListResp {
    const message = createBaseSearchArticleListResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => GetBlogListModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
