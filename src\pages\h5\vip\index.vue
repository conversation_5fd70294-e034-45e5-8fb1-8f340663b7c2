<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto pl-[0.4rem] py-[0.6rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">Búsqueda de proveedores y productos</div>
    </div>
    <div class="pt-[0.8rem] px-[0.4rem]">
      <div class="w-[6.7rem] h-[3.7688rem] rounded-[0.4rem] overflow-hidden">
        <video-you-tube
          :width="335"
          :height="188.438"
          youtubeId="qyp6EorLxbQ"
          titleCh="我们的合作伙伴怎么说"
          :poster="sourcingPoster"
        ></video-you-tube>
      </div>
      <div class="mt-[0.48rem] flex flex-col items-center">
        <div class="text-[0.56rem] leading-[0.68rem]">
          ¿Cómo encontrar proveedores confiables y obtener precios competitivos de productos sin venir a China?
        </div>
        <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.32rem]">
          Como su socio de abastecimiento más confiable, le brindamos lo mejor de la fabricación de China.
        </div>
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[4.12rem] h-[0.96rem] rounded-[10rem] mt-[1.2rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem]">Póngase en contacto</span>
        </n-button>
      </div>
    </div>
    <div class="pt-[1.6rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Servicio de agente de abastecimiento de China único</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.36rem]">
        Lo ayudamos a encontrar fábricas, obtener precios competitivos, hacer un seguimiento de la producción,
        garantizar la calidad y entregar los productos en la puerta.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="flex flex-col items-center gap-[0.68rem] mt-[1rem]">
        <div v-for="(step, index) in productServiceInfo" :key="index" class="flex items-start">
          <div class="w-full flex-shrink-0 flex flex-col items-center text-center">
            <img loading="lazy" :src="step.icon" :alt="vip" class="w-[1.92rem] h-[1.92rem] flex-shrink-0" />
            <div class="text-[0.36rem] leading-[0.36rem] mt-[0.32rem]">
              {{ step.title }}
            </div>
            <div class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.2rem]">
              {{ step.description }}
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[4.12rem] h-[0.96rem] rounded-[10rem] mt-[1rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem]">Trabajar con Chilat</span>
        </n-button>
      </div>
    </div>
    <div class="py-[1.6rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Valor de abastecimiento sostenible desde 2003</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.36rem]">
        Hemos visto que las empresas pierden tiempo y dinero innecesariamente cuando intentan obtener productos y
        servicios sin asistencia profesional. En CHILAT, eliminamos este riesgo por completo. Este es nuestro valor
        agregado.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="w-full flex flex-col gap-[0.4rem] mt-[0.8rem]">
        <div
          v-for="(item, index) in supplyValueInfo"
          :key="index"
          class="relative w-full min-h-[7.32rem] e border border-[#333] rounded-[0.4rem] text-center"
        >
          <img loading="lazy" :src="item.icon" :alt="vip" class="w-full" />
          <div class="text-[0.4rem] leading-[0.48rem] mt-[0.4rem]">
            {{ item.title }}
          </div>
          <div class="text-[0.28rem] leading-[0.336rem] text-[#7F7F7F] mt-[0.2rem] px-[0.24rem]">
            {{ item.description }}
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[4.12rem] h-[0.96rem] rounded-[10rem] mt-[1rem]"
        >
          <span class="text-[0.32rem] leading-[0.4rem]">Iniciar mi proyecto</span>
        </n-button>
      </div>
    </div>
    <div class="w-full h-[20.5rem] py-[1.6rem] px-[0.4rem] relative bg-[#F7F7F7] overflow-hidden">
      <div class="w-[19.6rem] h-[19.6rem] rounded-full bg-[#fff] absolute top-[1.2rem] left-[-3.82rem]"></div>
      <div class="relative z-1">
        <div class="text-[0.56rem] leading-[0.68rem]">Por qué elegirnos</div>
        <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.36rem]">
          No solo somos una empresa de abastecimiento de China, sino su socio a largo plazo. Adaptamos nuestros
          servicios para apoyar mejor a su negocio.
        </div>
        <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
        <img
          loading="lazy"
          src="@/assets/icons/busqueda-de-proveedores-y-productos/team.png"
          alt="vip"
          class="w-full mt-[0.8rem]"
        />
        <div class="text-[0.32rem] leading-[0.4rem] flex flex-col gap-[0.4rem] mt-[0.6rem]">
          <div class="flex items-start">
            <img
              loading="lazy"
              src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
              alt="vip"
              class="w-[0.56rem] mr-[0.4rem]"
            />
            <div>Servicio personal de asistente de habla hispana, comunicación sin barreras.</div>
          </div>
          <div class="flex items-start">
            <img
              loading="lazy"
              src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
              alt="vip"
              class="w-[0.56rem] mr-[0.4rem]"
            />
            <div>Nos enfocamos en servir a clientes latinoamericanos durante 22 años, los conocemos mejor.</div>
          </div>
          <div class="flex items-start">
            <img
              loading="lazy"
              src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
              alt="vip"
              class="w-[0.56rem] mr-[0.4rem]"
            />
            <div>
              Como empresa confiable de abastecimiento en China, contamos con servicios de cadena de suministro sólidos
              y competitivos.
            </div>
          </div>
          <div class="flex items-start">
            <img
              loading="lazy"
              src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
              alt="vip"
              class="w-[0.56rem] mr-[0.4rem]"
            />
            <div>
              Damos servicio a empresas de todos los tamaños; Desde pymes hasta grandes corporaciones y nuestro equipo
              puede atender exactamente lo que le falta a su negocio.
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn w-[5.8rem] h-[0.96rem] rounded-[10rem] mt-[1rem]"
          >
            <span class="text-[0.32rem] leading-[0.4rem]">Obtenga su consulta gratuita</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="pt-[1.6rem] pb-[2rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Que dicen nuestros socios</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem] mx-auto"></div>
      <div class="w-[6.7rem] h-[3.7688rem] rounded-[0.4rem] overflow-hidden mt-[0.8rem] mx-auto">
        <video-you-tube
          :width="335"
          :height="188.438"
          youtubeId="D7RZTZdWX0o"
          titleCh="我们的合作伙伴怎么说"
          :poster="feedbackPoster"
        ></video-you-tube>
      </div>
    </div>
    <div
      class="rounded-[0.4rem] w-full h-[4.02rem] text-[#fff] pt-[0.36rem] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.576rem] font-medium">¿Ya tiene un proveedor?</div>
      <div class="text-[0.32rem] leading-[0.4rem] mt-[0.32rem]">
        Deje que nuestro equipo de control de calidad proteja la seguridad de su importación
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[4.4rem] h-[1.28rem] rounded-[10rem] mt-[0.36rem] global-contact-btn"
      >
        <span class="text-[0.32rem] leading-[0.32rem] mr-[0.12rem]">Más información</span>
        <img
          loading="lazy"
          alt="vip"
          class="arrow-icon w-[0.88rem]"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
        />
      </n-button>
    </div>
    <div class="py-[1.6rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Preguntas más frecuentes</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.36rem] mx-auto"></div>
      <n-collapse
        class="mt-[0.6rem] w-[6.7rem] mx-auto"
        :show-arrow="false"
        :on-update:expanded-names="onUpdateExpandedNames"
      >
        <n-collapse-item
          v-for="(question, index) in questionInfo"
          :key="index"
          :name="question.question"
          class="py-[0.52rem]"
          :class="{
            'border-b-1 border-[#F2F2F2]': index === questionInfo.length - 1,
          }"
        >
          <template #header>
            <div
              class="flex"
              :class="{
                'text-[#e50113]': pageData.expandedQuestions.includes(question.question),
              }"
            >
              <div
                class="text-[0.32rem] text-[#7F7F7F] mr-[0.28rem]"
                :class="{
                  'text-[#e50113]': pageData.expandedQuestions.includes(question.question),
                }"
              >
                0{{ index + 1 }}
              </div>
              <span class="text-[0.36rem] leading-[0.44rem]">{{ question.question }}</span>
            </div>
          </template>
          <template #header-extra="{ collapsed }">
            <img
              loading="lazy"
              v-if="collapsed"
              alt="vip"
              class="w-[0.44rem] ml-[0.28rem]"
              src="@/assets/icons/common/expand-red.svg"
            />
            <img
              loading="lazy"
              v-else
              alt="vip"
              class="w-[0.44rem] ml-[0.28rem]"
              src="@/assets/icons/common/collapse-red.svg"
            />
          </template>
          <template #arrow><span class="hidden"></span></template>
          <div
            v-html="question.answer"
            data-spm-box="article-inner-link"
            class="text-[0.32rem] leading-[0.48rem] text-[#333] ml-[0.64rem] mr-[0.72rem]"
          ></div>
        </n-collapse-item>
      </n-collapse>
    </div>
    <MobileWhatsAppContact />
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import sourcingPoster from "@/assets/icons/busqueda-de-proveedores-y-productos/sourcing-poster.png";
import step1 from "@/assets/icons/busqueda-de-proveedores-y-productos/step1.png";
import step2 from "@/assets/icons/busqueda-de-proveedores-y-productos/step2.png";
import step3 from "@/assets/icons/busqueda-de-proveedores-y-productos/step3.png";
import step4 from "@/assets/icons/busqueda-de-proveedores-y-productos/step4.png";
import step5 from "@/assets/icons/busqueda-de-proveedores-y-productos/step5.png";
import supplierIntegration from "@/assets/icons/busqueda-de-proveedores-y-productos/supplier-integration.png";
import paymentSecurity from "@/assets/icons/busqueda-de-proveedores-y-productos/payment-security.png";
import saveTime from "@/assets/icons/busqueda-de-proveedores-y-productos/save-time.png";
import feedbackPoster from "@/assets/icons/busqueda-de-proveedores-y-productos/feedback-poster.png";
import redBg from "@/assets/icons/quienes-somos/mobile-red-bg.png";

useHead({
  title: "VIP | Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/vip/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { link: "/h5/servicios", text: "Servicios" },
  {
    link: "/h5/busqueda-de-proveedores-y-productos",
    text: "Búsqueda de proveedores y productos",
  },
];

const productServiceInfo = [
  {
    icon: step1,
    title: "Cumpla con sus necesidades",
    description:
      "Simplemente háganos saber qué tipo de productos necesita, especificaciones y la cantidad que necesita.",
  },
  {
    icon: step2,
    title: "Proveedores de abastecimiento",
    description:
      "Nuestro experto en abastecimiento le ayudará a buscar proveedores en China y le ofrecerá el precio más competitivo de acuerdo con los requisitos de su producto.",
  },
  {
    icon: step3,
    title: "Muestreo y producción",
    description:
      "Una vez que apruebe la muestra que le enviamos, firmaremos un contrato chino con la fábrica y daremos seguimiento a la producción.",
  },
  {
    icon: step4,
    title: "Control de calidad",
    description:
      "Realizamos un control de calidad al recoger la mercancía en nuestro almacén y le proporcionamos informes de inspección antes del envío.",
  },
  {
    icon: step5,
    title: "Logística",
    description:
      "Brindamos servicio de entrega puerta a puerta a su solicitud, incorporando transportistas para recoger y entregar los productos.",
  },
];

const supplyValueInfo = [
  {
    icon: supplierIntegration,
    title: "Proveedor todo en uno",
    description:
      "Independientemente de los productos que necesite, lo ayudaremos a encontrar fábricas, obtener excelentes tarifas, garantizar una calidad superior y entregar los productos de manera segura.",
  },

  {
    icon: paymentSecurity,
    title: "Proteja la seguridad del pago",
    description:
      "Verificamos al proveedor antes de firmar contratos y realizamos pagos después de la inspección de los productos, lo protegemos de estafadores y solicitantes en línea que parecen estar en todas partes en línea.",
  },
  {
    icon: saveTime,
    title: "Ahorre tiempo y dinero",
    description:
      "Puede gastar mucho tiempo y dinero en proveedores no calificados y puede encontrar su proveedor ideal después de pasar mucho tiempo investigando. Con CHILAT le ayudaremos a gestionar sus proveedores en su nombre y ya no tendrá este tipo de problemas.",
  },
];

const questionInfo = [
  {
    question: "¿Qué tipo de productos abastece su empresa?",
    answer:
      "En términos generales, abastecemos principalmente bienes de consumo, como ropa, joyas, juguetes, productos electrónicos, muebles, utensilios de cocina, ferretería y herramientas, hogar y jardín, etc. Pero, de hecho, podríamos obtener cualquier producto que necesite, por ejemplo, acabamos de comprar una excavadora de segunda mano para nuestro cliente recientemente.",
  },
  {
    question: "Si ya tengo mis propios proveedores, ¿cómo puedo trabajar con Chilat?",
    answer:
      "Si tiene su propio proveedor, lo ayudaremos a verificar la fábrica e inspeccionar la calidad de los productos, también organizaremos el envío. Si los proveedores provienen de todo el país, podríamos consolidar la mercadería en nuestro almacén y entregarle un contenedor completo.",
  },
  {
    question: "¿Cuál es el costo de su servicio de abastecimiento?",
    answer:
      "Las dos primeras etapas de nuestros servicios son totalmente gratuitas. Solo cobramos una tarifa de servicio del 3 ~ 5% dependiendo del monto de su pedido para las etapas posteriores de los servicios. Eso significa que si no está satisfecho con la cotización, no tiene que pagar nada.",
  },
  {
    question: "¿Cómo obtiene Chilat precios más competitivos?",
    answer:
      "<div>En China, no todas las fábricas tienen presencia en línea. Trabajamos con miles de fabricantes que no están disponibles en sitios como Alibaba.com o 1688.com, y obtenemos precios competitivos y económicos de ellos.</div><div style='margin-top: 0.12rem;'>También llevamos a cabo nuestra debida diligencia con todos los proveedores que obtenemos utilizando nuestro acceso a bases de datos del gobierno y otros terceros para asegurarnos de que estamos tratando con una fábrica (y no con una empresa comercial que dice ser una fábrica) y la fábrica es confiable y tiene un historial de fabricación.</div>",
  },
  {
    question: "No tengo experiencia en la importación de China, ¿cómo puedo elegir su servicio?",
    answer:
      "<div>Si tiene la intención de importar desde China, no importa si no tiene experiencia en la importación, nuestro asistente de compras le brindará servicios gratuitos de consultoría de importación y lo guiará a través de todo el proceso de importación.</div><div style='margin-top: 0.12rem;'>Muchos clientes se han convertido en expertos en importación para principiantes en importación bajo nuestra guía.</div>",
  },
];

const pageData = reactive(<any>{
  expandedQuestions: [],
});

function onUpdateExpandedNames(ids: any) {
  pageData.expandedQuestions = ids;
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/busqueda-de-proveedores-y-productos/mobile-header-bg.png");
  background-repeat: no-repeat;
}
:deep(.n-collapse .n-collapse-item) {
  margin: 0;
}
:deep(.n-collapse .n-collapse-item .n-collapse-item__header) {
  padding: 0;
  align-items: flex-start;
}
:deep(.n-collapse .n-collapse-item:not(:first-child)) {
  border-top: 0.02rem solid #f2f2f2;
}
</style>
