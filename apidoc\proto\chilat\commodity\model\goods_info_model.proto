syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "chilat/commodity/commodity_common.proto";
import "chilat/commodity/param/goods_info_param.proto";
import "common/business.proto";

message GoodsInfoPageData {
    common.Page page = 1;
    repeated GoodsListDataModel data = 2; // 商品列表数据
    int32 onlineCount = 3; // 已上架商品数
    int32 offlineCount = 4; // 未上架商品数
    int32 draftCount = 5; // 未发布商品数
    int32 recycleCount = 6; // 回收商品数
}

message GoodsInfoPageResp {
    common.Result result = 1;
    GoodsInfoPageData data = 2;
}

message GoodsInfoDetailResp {
    common.Result result = 1;
    GoodsInfoModel data = 2;
}

// 商品信息选项
message GoodsAttributeItem {
    string itemId = 3; // item id
    string itemName = 4; // item名称
    string itemAlias = 5; // item别名（可为空）
}

message GoodsAttributeOptionModel {
    string id = 1;
    string name = 2;
    GoodsAttributeItemType type = 3; // 属性选项类型（10输入框、20多选项、30下拉框）
    repeated GoodsAttributeItem items = 4; //属性选项列表
}

message GoodsInfoOptionModel {
    repeated common.IdNameModel priceUnitList = 1; // 价格单位列表
//    repeated common.IdNameModel freightTemplateList = 3; // 运费模板列表
//    repeated common.IdNameModel brandOptionList = 4; // 品牌选项列表
    repeated common.IdNameModel specOptionList = 5; // 规格选项列表
    repeated GoodsAttributeOptionModel attrOptionList = 6; // 属性选项列表
    repeated GoodsExtendItem attrItemList = 7; // 属性参数列表
}

message GoodsInfoOptionResp {
    common.Result result = 1;
    GoodsInfoOptionModel data = 2;
}

// 商品列表数据
message GoodsListDataModel {
    string id = 10; //商品ID
    string goodsNo = 20; // 商品编号-主数据
    string goodsName = 30; // 商品名称-主数据
    string categoryName = 40; //商品分类名称
    string mainImageUrl = 50; //商品主图
    int32 totalStockQty = 60; //总库存数（可用库存数）
    GoodsOnlineState onlineState = 71; // 上架状态
    string onlineStateDesc = 72; // 上架状态描述
    GoodsSourceType sourceType = 73; // 商品来源
    string sourceTypeDesc = 74; // 商品来源描述
    GoodsUpdateType updateType = 75; // 商品更新规则
    string updateTypeDesc = 76; // 商品更新规则描述
    SourceGoodsState sourceGoodsState = 77; // 源商品状态
    string sourceGoodsStateDesc = 78; // 源商品状态描述
    int64 publishTime = 81; // 发布时间
    string publishAccount = 82; // 发布人
    int64 udate = 90; // 更新时间
    repeated GoodsListSkuDataModel skuList = 100; //SKU列表
    string tag = 110; // 1688找货LATGID
    bool isAudit = 111; // 是否校验
    bool isIllegal = 112; // 是否侵权
    string bucketNo = 113; // 校验桶号
    repeated common.IdNameModel goodsTags = 120; // 商品标签
    double m3FreightUsd = 130; //1立方米预估运费，单位：美元
}

// 商品列表SKU数据
message GoodsListSkuDataModel {
    string skuId = 10; //SKU ID
    string skuNo = 20; //SKU货号
    string skuImageUrl = 30; //SKU图片URL
    repeated common.NameValueModel specList = 40;
    double supplierPriceRMB = 51; //采购价-人民币
    double salePriceRMB = 52; //销售价-人民币
    double salePriceUSD = 53; //销售价-美元
    int32 stockQty = 60; //库存数
    GoodsOnlineState skuState = 71; // 上架状态
    string skuStateDesc = 72; // 上架状态描述
}


// 商品信息
message GoodsInfoModel {
    string id = 10; //商品ID
    string goodsNo = 20; // 商品编号-主数据
//    string brandId = 4; // 品牌ID
//    int32 idx = 2; //顺序
//    string brandName = 62; // 品牌名称
    string categoryId = 30; // 商品分类ID
//    string categoryName = 61; //商品分类名称
    string goodsName = 40; // 商品名称-主数据
//    string goodsTitle = 7; // 商品标题-主数据
    GoodsOnlineState onlineState = 51; //上架状态
    string onlineStateDesc = 52; //上架状态描述
//    bool enabled = 9; // 启用/禁用（后端维护，商品信息发布完整后，自动启用）
    string goodsPriceUnitId = 61; // 商品价格单位ID，原goodsPriceUnit
    string goodsPriceUnitName = 62; // 商品价格单位名称
    string videoCoverImage = 70; // 视频封面图片（原coverImage）
    string videoUrl = 80; // 视频URL（原video）
    repeated string goodsImageList = 90; // 商品图片组
    string goodsRemark = 100; // 商品备注
    int32 minBuyQuantity = 110; //最小购买数量（起订量）
    int32 minIncreaseQuantity = 120; //最小加购数量（一次加购数量）
    double priceFactor = 130; //价格系数
    double goodsLength = 140; // 商品长（单位：cm）
    double goodsWidth = 150; // 商品宽（单位：cm）
    double goodsHeight = 160; // 商品高（单位：cm）
    double goodsWeight = 170; // 商品重量（单位：kg）
    int32 xporPackageInsideCount = 171; // 发布到 XPorMayor 用的 包装含量/装箱数量
    int32 packageInsideCount = 175; // 包装内含货品的数量（包装含量/装箱数量）
    string packageInsideUnit = 176; // 包装内含货品的数量单位
//  int32 purchaseLimitation = 30; // 限购数量，大于等于0的整数,0表示不限购
//  string templateId = 31; // 运费模板id-附属属性
    repeated GoodsExtendItem specItemList = 190; // 规格参数
    repeated common.SkuStepRange skuStepRanges = 195; // SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同）
    repeated SkuItem skuList = 200; // sku数据
    repeated GoodsExtendItem attrItemList = 210; // 属性参数
    repeated common.IdNameModel goodsTagList = 215; //标签列表
    string goodsDesc = 220; // 商品描述（源goodsPcDesc）
    int64 publishTime = 230; // 发布时间-附属属性
    string publishAccount = 240; // 发布人-附属属性
    string crawlGoodsId = 300; //采集商品ID
    GoodsSourceType sourceType = 310; //商品来源类型（人工抓取、1688 同步、人工创建）
    string sourceGoodsId = 320; //源商品ID
    SourceGoodsState sourceGoodsState = 330; //源商品状态
    GoodsUpdateType goodsUpdateType = 340; //商品更新规则
    string sellerId = 350; //卖家ID
    string goodsNameCn = 360; // 商品名称-中文
    string shopId = 361; // 店铺id
    string goodsPriceUnitNameCn = 362; // 商品计价单位名称中文
    string sourceShopId = 363; // 源店铺id
    double m3FreightUsd = 370; //1立方米预估运费，单位：美元
    repeated PackingSchemeItem packingSchemes = 410; //装箱方案数组
}

message GoodsImportResp {
    common.Result result = 1;
    GoodsImportModel data = 2;
}

message GoodsImportModel {
    int32 totalCount = 1; // 导入商品总数量
    int32 successCount = 2; // 导入成功数量
    int32 failCount = 3; // 导入失败数量
    string downloadFailUrl = 4; // 下载失败商品URL
}

message ChangeGoodsOnlineStateResp {
    common.Result result = 1;
    ChangeGoodsOnlineStateModel data = 2;
}

message ChangeGoodsOnlineStateModel {
    bool successNotNeedConfirm = 1; //更新成功，不需要再次确认（false表示需要弹出确认框）
    repeated ChangeGoodsOnlineFailInfoModel failGoodsList = 2; //操作失败的商品列表
    repeated ChangeGoodsOnlineFailInfoModel unAuditGoodsList = 3; //未校验的商品列表
    repeated ChangeGoodsOnlineFailInfoModel illegalGoodsList = 4; //非法的商品列表
    ChangeGoodsOnlineStateParam confirmParam = 5; //确认后，再次提交需要使用的参数
}

message ChangeGoodsOnlineFailInfoModel {
    string id = 10; //商品ID
    string goodsNo = 20; //商品编码
    string goodsName = 30 ; //商品名称
    string imageUrl = 40; //商品图片
}

//更新价格系统的返回
message UpdatePriceFactorResp {
    common.Result result = 1;
    UpdatePriceFactorModel data = 2;
}

//更新价格系统的返回对象
message UpdatePriceFactorModel {
    string id = 10; //商品ID
    double beforePriceFactor = 20; //更新前的价格系数
    double afterPriceFactor = 30; //更新后的价格系数
    bool changed = 40; //是否修改（若更新前后的价格系数一样，则不做修改）
}

message SyncToXResp {
    common.Result result = 1;
    SyncToXModel data = 2;
}

message SyncToXModel {
    repeated string goodsNos = 1; //缺少包装信息的商品编号
    repeated SyncToXResultModel syncResult = 2; //同步结果
}

message SyncToXResultModel {
    string goodsNo = 1; //商品编号
    string goodsId = 2; //商品ID
    bool success = 3; //是否成功
    string errorMsg = 4; //错误信息
}

//商城商品（营销规则）搜索结果
message MallGoodsListDataResp {
    common.Result result = 1;
    MallGoodsListDataModel data = 2;
}

message MallGoodsListDataModel {
    common.Page page = 10; //商品列表的分页信息
    repeated MallGoodsListDataItemModel goodsList = 20; //商品列表
    string tokenizeWords = 30; //搜索关键字的分词价格（用空格分隔）
}

message MallGoodsListDataItemModel {
    string goodsId = 10; // 商品ID
    string goodsNo = 20; // 商品编码
    string goodsName = 30; // 商品名称
    string goodsTitle = 40; // 商品标题
    string categoryName = 41; // 商品分类名称（后台分类名称）
    string goodsPriceUnitName = 45; // 价格单位
    common.CurrencyType currency = 46; // 价格对应的货币
    double minPrice = 50; // 最低价
    double maxPrice = 55; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
    int32 minBuyQuantity = 110; //最小购买数量（起订量）
    int32 minIncreaseQuantity = 120; //最小加购数量（一次加购数量）
    string mainImageUrl = 60; // 商品主图
    float hitScore = 70; //搜索相关度得分
}
