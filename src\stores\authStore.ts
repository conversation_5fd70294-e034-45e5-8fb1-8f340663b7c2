import { defineStore } from "pinia";

export const useAuthStore = defineStore("use-auth", {
  state: () => ({
    locale: "es",
    showAnchor: false, // 控制悬浮按钮展示
    showPageFooter: false, // 控制底部消息展示
  }),
  getters: {
    getShowAnchor(state: any) {
      return state.showAnchor;
    },
    getShowPageFooter(state: any) {
      return state.showPageFooter;
    },
  },
  actions: {
    setShowAnchor(flag: boolean) {
      this.showAnchor = flag;
    },
    setShowPageFooter(flag: boolean) {
      this.showPageFooter = flag;
    },
  },
  persist: process.client && {
    storage: localStorage,
  },
});
