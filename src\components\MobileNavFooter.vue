<template>
  <footer class="w-full bg-[#e50113] text-white py-[1.2rem] px-[0.4rem]" id="nav-footer">
    <img src="@/assets/icons/common/logo-white.png" alt="Chilat Logo" class="w-[3.16rem]" loading="lazy" />
    <!-- 顶部区域：介绍 -->
    <div class="flex flex-col mt-[0.68rem]">
      <div class="w-full text-[0.32rem] leading-[0.42rem]">
        <div>
          Somos el agente de compra y asesoramiento más grande de China dedicado a servir a importadores de habla
          hispana.
        </div>
        <div class="text-[0.32rem] leading-[0.42rem] mt-[0.12rem]">
          Brindamos a nuestros clientes un conjunto de soluciones para todas las importaciones desde China.
        </div>
      </div>

      <!-- 导航链接区域 -->
      <div class="w-full mt-[0.84rem] flex flex-col gap-[0.56rem] text-[0.28rem] leading-[0.28rem]">
        <!-- NOSOTROS 栏目 -->
        <div>
          <div class="text-[0.32rem] leading-[0.32rem] font-medium mb-[0.36rem]">NOSOTROS</div>
          <ul>
            <li v-for="(item, index) in nosotrosLinks" :key="index" class="mb-[0.2rem]">
              <a :href="item.path" class="hover:font-medium">{{ item.label }}</a>
            </li>
          </ul>
        </div>

        <!-- SERVICIOS 栏目 -->
        <div>
          <div class="text-[0.32rem] leading-[0.32rem] font-medium mb-[0.36rem]">SERVICIOS</div>
          <ul>
            <li v-for="(item, index) in serviciosLinks" :key="index" class="mb-[0.2rem]">
              <a :href="item.path" class="hover:font-medium">{{ item.label }}</a>
            </li>
          </ul>
        </div>

        <!-- FERIAS Y MERCADO 栏目 -->
        <div>
          <div class="text-[0.32rem] leading-[0.32rem] font-medium mb-[0.36rem]">FERIAS Y MERCADO</div>
          <ul>
            <li v-for="(item, index) in feriasLinks" :key="index" class="mb-[0.2rem]">
              <a :href="item.path" class="hover:font-medium">{{ item.label }}</a>
            </li>
          </ul>
        </div>
      </div>

      <!-- 社交媒体图标 -->
      <div class="flex flex-wrap gap-[0.24rem] mt-[0.84rem]">
        <a href="https://twitter.com/chilatlatam" target="_blank">
          <img src="@/assets/icons/common/twitter.svg" alt="Twitter" class="twitter-icon w-[0.8rem]" loading="lazy" />
        </a>
        <a href="https://www.tiktok.com/@chilatlatam" target="_blank">
          <img src="@/assets/icons/common/tiktok.svg" alt="TikTok" class="tiktok-icon w-[0.8rem]" loading="lazy" />
        </a>
        <a target="_blank" href="https://www.youtube.com/@chilatlatam8360">
          <img src="@/assets/icons/common/youtube.svg" alt="YouTube" class="youtube-icon w-[0.8rem]" loading="lazy" />
        </a>
        <a target="_blank" href="https://www.facebook.com/Chilatlatam-1835586930005001/">
          <img
            src="@/assets/icons/common/facebook.svg"
            alt="Facebook"
            class="facebook-icon w-[0.8rem]"
            loading="lazy"
          />
        </a>
        <a target="_blank" href="https://www.linkedin.com/company/chilat/?trk=mini-profile">
          <img
            src="@/assets/icons/common/linkedin.svg"
            alt="LinkedIn"
            class="linkedin-icon w-[0.8rem]"
            loading="lazy"
          />
        </a>
        <a target="_blank" href="https://www.instagram.com/chilatlatam/">
          <img src="@/assets/icons/common/ins.svg" alt="Instagram" class="instagram-icon w-[0.8rem]" loading="lazy" />
        </a>
      </div>
    </div>

    <!-- 底部区域：版权信息 -->
    <div class="mt-[0.84rem] flex flex-col text-[0.24rem] leading-[0.24rem]">
      <a href="/quienes-somos/" class="text-[0.32rem] leading-[0.32rem]">Aprender más</a>
      <div class="mt-[0.84rem]">
        <span>Mapa del sitio</span>
        <span class="mx-[0.04rem]">/</span>
        <a href="/h5/article/privacidad_terminos" class="hover:font-medium"
          >Política de privacidad y Términos de Uso
        </a>
      </div>
      <div class="mt-[0.24rem] leading-[0.36rem]">
        Copyright © 2018 义乌铭展进出口有限公司 (Yiwu MingZhan Import & Export Co., Limited, Chilat.com). All rights
        reserved.
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// 定义导航链接数据
const nosotrosLinks = [
  { label: "Quiénes somos", path: "/h5/quienes-somos/" },
  { label: "Cómo trabajamos", path: "/h5/como-trabajarnos/" },
];

const serviciosLinks = [
  {
    label: "Asesoría de compras",
    path: "/h5/servicios#asesoria-de-compras/",
  },
  {
    label: "Búsqueda de proveedores y productos",
    path: "/h5/busqueda-de-proveedores-y-productos/",
  },
  {
    label: "Validación de fabricantes",
    path: "/h5/viaja-al-mercado-de-yiwu/",
  },
  {
    label: "Viajes de negocios a China",
    path: "/h5/servicios#viajes-de-negocios-a-china/",
  },
  {
    label: "Agente de compras",
    path: "/h5/viajar-a-china/",
  },
  {
    label: "Control de calidad",
    path: "/h5/servicios#control-de-calidad/",
  },
  {
    label: "Consolidación de cargas y logística",
    path: "/h5/consolidacion-de-cargas-y-logistica/",
  },
  {
    label: "Fotos del producto y catálogo",
    path: "/h5/servicios#fotos-del-producto-y-catalogo/",
  },
];

const feriasLinks = [
  { label: "Guangzhou", path: "/h5/feria-de-canton/" },
  { label: "Yiwu", path: "/h5/donde-queda-yiwu/" },
];
</script>

<style scoped lang="scss">
.footer-container {
  background-color: #e50113;
  color: white;
}

a {
  color: white;
  text-decoration: none;
}

.twitter-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/twitter-red.svg");
  }
}

.tiktok-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/tiktok-red.svg");
  }
}

.facebook-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/facebook-red.svg");
  }
}

.youtube-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/youtube-red.svg");
  }
}

.instagram-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/ins-red.svg");
  }
}

.linkedin-icon {
  transition: all 0.4s ease;
  &:hover {
    content: url("@/assets/icons/common/linkedin-red.svg");
  }
}
</style>
