syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.param";

import "chilat/user/user_common.proto";
import "common.proto";

message UserPageQueryParam {
  common.PageParam page = 1;
  UserQueryParam query = 2;
}

message UserQueryParam {
  string username = 1; // 用户名
  string realName = 2; // 姓名
  bool  enabled = 3; // 是否启用
  int32 systemId = 4;
}

message UserSaveParam {
  string id = 1; // 用户ID
  string username = 2; // 用户名
  string realName = 3; // 姓名
  string password = 4; // 密码（可选）
  string phone = 5; // 手机号
  string email = 6; // 邮箱
  repeated JobDuty duties = 7; // 岗位职责
  int32 inquiryCount = 8; // 询盘客户数
  string batchId = 9; //日志批次号
  int32 systemId = 10;
}

message GetPermissionParam {
  string id = 1;
  int32 systemId = 2;
}

message SetPermissionParam {
  string id = 1;
  repeated string values = 2;
  int32 systemId = 3;
}

message PurchaserSaveParam {
  string userId = 1; // 用户ID
  string username = 2; // 用户名
 repeated string goodsLookingId = 3; // 询盘id
}

message ListByJobDutyParam {
  JobDuty jobDuty = 1; //根据岗位职责拉取员工列表
  bool needAll = 2; //是否需要全部该职责的员工列表
}
