syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.model";

import "common.proto";
message SqlReportPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated SqlReportListModel data = 3;
}

// SQL报表列表数据
message SqlReportListModel {
  string id = 10; //报表ID
  string reportName = 20; //报表名称
  string description = 30; //说明（Text格式，前端显示时，类似于<PRE>标签格式显示）
  int32 queryCount = 40; //查询次数
  int32 exportCount = 50; //导出次数
  int64 createTime = 110; //创建时间
  int64 modifyTime = 120; //修改时间
}

message SqlReportDataTableResp {
  common.Result result = 1;
  SqlReportDataTableModel data = 2;
}

message SqlReportDataTableModel {
  string id = 10; //报表ID
  string reportName = 20; //报表名称
  string htmlTable = 30; //HTML格式的数据表格
}

// SQL报表筛选参数列表
message SqlReportFilterOptionsResp {
  common.Result result = 1;
  SqlReportFilterInfoModel data = 2;
}

// SQL报表筛选参数列表数据
message SqlReportFilterInfoModel {
  string reportId = 10; //报表ID
  string reportName = 20; //报表名称
  repeated SqlReportFilterOptionModel filterOptions = 30; //过滤参数选项
}

// SQL报表筛选参数列表数据
message SqlReportFilterOptionModel {
  int32 rowNo = 10; //显示行号（从1开始）
  string displayName = 20; //显示字段名（在页面上显示的名称）
  string fieldName = 30; //提交字段名（查询报表时的名称）
  string fieldType = 40; //字段类型（可选值：date 日期，input 输入框）
  string defaultValue = 50; //默认值
}
