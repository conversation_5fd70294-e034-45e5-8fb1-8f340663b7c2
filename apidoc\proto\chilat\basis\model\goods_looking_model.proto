syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";

message QueryInquiryResp {
  common.Result result = 1;
  InquiryModel data = 2;
}

message InquiryModel {
  string goodsLookingNo = 1; //询盘单号
  int32 totalGoodsCount = 2; // 询盘总数量
  double subTotal = 3; //询盘总价
  repeated InquirySkuModel skus = 4; //询盘商品
  string remark = 5; //客户备注
  string id = 6; //id
  int64 submitTime = 7; //询盘时间
  string countryName = 8; //国家
  string whatsapp = 9; //whatsapp
  string submitName = 10; //姓名
  string email = 11; //邮箱
  string postcode = 12; //邮编
  string address = 13; //地址
  string countryId = 14; //国家
  string areaCode = 15; //区号
  bool isFirstSubmit = 20; //是否首次询盘
  double totalEstimateFreight = 181; //预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 182; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 183; //参与预估运费的商品数量
  int32 siteId = 210; //站点ID（即配送国家ID）
  string siteCode = 220; //站点代码（两位配送国家代码；大写英文字母）
  string siteName = 230; //站点名称（即配送国家名称）
  string siteLogo = 240; //站点LOGO图片（国家标识图片URL，46x32像素）
}

message InquirySkuModel {
  string skuId = 100; //sku id
  string goodsId = 1; //商品id
  string goodsName = 2; //商品名称
  string skuImage = 3; //sku图片
  int32 buyQuantity = 4; //购买数量
  string priceUnitName = 5; //价格单位
  double salePrice = 6; //销售价
  double totalSalePrice = 7; //销售总价
  double supplierPrice = 8; //供应商价格
  string supplyLink = 9;//货源链接
  int32 packageInsideCount = 10; //包装含量
  double goodsLength = 11; //长
  double goodsWidth = 12; //宽
  double goodsHeight = 13; //高
  double goodsWeight = 14; //重量
  map<string, SpecModel> specMap = 15; //specMap
  repeated SkuSpecModel specs = 16; //规格值
  double estimateFreight = 181; //预估运费
  string routeId = 182; //线路id
}

message specGroupModel {
  string groupName = 2;
  repeated SpecModel specList = 3;
}

message SkuSpecModel {
  string specId = 1; //规格ID
  string specName = 2; //规格名称
  string itemId = 3; //规格值ID
  string itemName = 4; //规格值
  string imageUrl = 5; //图片
  string color = 6; //颜色
}

message SpecModel {
  repeated string specName = 2;
}

message GoodsLookingPageListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated InquiryModel data = 3;
}