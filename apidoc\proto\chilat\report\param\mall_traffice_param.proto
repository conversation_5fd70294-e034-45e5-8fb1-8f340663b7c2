syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.param";

import "common.proto";

// 时区类型
enum TimeZoneType {
    TIME_ZONE_TYPE_UNSPECIFIED = 0; //不限
    TIME_ZONE_TYPE_WEST = 1; //西半球
    TIME_ZONE_TYPE_EAST = 2; //东半球
}

// 查询访客列表参数
message QueryVisitorListParam {
    common.PageParam page = 10; //分页（默认当前页1，默认分页大小20）
    TimeZoneType timeZoneType = 15; //时区类型
    string visitorId = 20; //访客ID（可选，多个用换行符分隔）
    string startDate = 30; //起始访问时间（格式：yyyy-MM-dd，最早2024-05-23）
    string endDate = 40;  //结束访问时间（格式：yyyy-MM-dd，时间跨度不超过1个月）
}

// 查询访客PV参数
message QueryPageVisitParam {
    string visitorId = 10; //访客ID（必填）
    string visitDate = 20; //访问日期（默认取最近有数据的日期）
}

// 查询会话列表参数
message QueryVisitSessionListParam {
    common.PageParam page = 10; //分页（默认当前页1，默认分页大小20）
    TimeZoneType timeZoneType = 15; //时区类型
    string visitorId = 20; //访客ID（可选，多个用换行符分隔）
    string visitSessionId = 25; //会话ID（可选，多个用换行符分隔）
    int32 sessionSeqNo = 26; //会话序号
    string startDate = 30; //起始访问时间（格式：yyyy-MM-dd，最早2024-05-23）
    string endDate = 40;  //结束访问时间（格式：yyyy-MM-dd，时间跨度不超过1个月）
}

// 查询会话PV参数
message QueryPageVisitBySessionParam {
    string visitorId = 10; //访客ID（必填）
    int32 sessionSeqNo = 20; //会话序号
    //string visitSessionId = 30; //会话ID
}


// 查询访客页面操作事件参数
message QueryPageEventParam {
    common.PageParam page = 10; //分页（默认当前页1，默认分页大小20）
    string visitorId = 20; //访客ID（必填）
}

message UpdateDailyReportParam {
    string startDate = 10; //起始访问时间（格式：yyyy-MM-dd，最早2024-05-23）
    string endDate = 20;  //结束访问时间（格式：yyyy-MM-dd，时间跨度不超过1年）
}