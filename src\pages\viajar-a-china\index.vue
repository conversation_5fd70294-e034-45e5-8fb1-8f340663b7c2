<template>
  <div class="page-container">
    <div class="page-header">
      <div class="w-[1280px] px-[64px] py-[36px] mx-auto">
        <div class="relative z-2">
          <div class="w-[550px] text-[52px] leading-[62px] font-medium mt-[40px]">
            Conozca el<br />centro de la excelencia<br />en la fabricación
          </div>

          <div class="w-[480px] text-[26px] leading-[52px] mt-[52px]">
            Embárquese en un viaje<br />transformador de asesoramiento a China
          </div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto text-[#333] px-[64px] pb-[190px]">
      <div class="w-full mt-[120px] text-center">
        <div class="text-[34px] leading-[34px] font-medium">
          ¿Es necesario para usted hacer un viaje a China para importar?
        </div>
        <div class="w-full text-[18px] leading-[22px] text-[#7F7F7F] mt-[16px]">
          <div>
            ¡Absolutamente! Imagínese tener reuniones cara a cara con proveedores y establecer relaciones a largo plazo.
          </div>
          <div>Un viaje a China puede ser una experiencia valiosa para las empresas que buscan:</div>
        </div>
        <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
        <div class="w-full mt-[78px] flex justify-between">
          <div v-for="(item, index) in visitChinaData" :key="index" class="w-[230px] flex flex-col items-center">
            <img loading="lazy" :src="item.imgUrl" alt="viajar a china" class="w-[48px]" />
            <div class="min-h-[40px] text-[20px] leading-[20px] mt-[18px] flex items-center">
              {{ item.title }}
            </div>
            <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[14px]">
              {{ item.desc }}
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[500px] mt-[94px] h-[50px]"
          >
            <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
      <div class="w-full mt-[170px]">
        <div class="text-[34px] leading-[34px] font-medium text-center">¿De qué nos ocuparemos?</div>
        <div class="w-[954px] mx-auto text-[18px] leading-[22px] text-[#7F7F7F] text-center mt-[16px]">
          Le brindamos un servicio integral para compras en China, ayudándole a comprar productos de buena calidad con
          mejor precio en China y resolviendo problemas comunes que encuentre durante el proceso de compra.
        </div>
        <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
        <div class="flex justify-between flex-wrap mt-[78px]">
          <div
            v-for="(item, index) in visitProcData"
            :key="index"
            class="relative w-[372px] h-[446px] bg-white border border-[#333] rounded-[20px] pt-[248px] px-[27px] mb-[28px]"
          >
            <img loading="lazy" :src="item.imgUrl" alt="viajar a china" class="absolute top-0 left-0" />
            <div class="text-[20px] leading-[20px]">
              {{ item.title }}
            </div>
            <n-space vertical :style="{ gap: '4px 0' }" class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px]">
              <div v-for="desc in item.descData" :key="desc">
                {{ desc }}
              </div>
            </n-space>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[500px] mt-[94px] h-[50px]"
          >
            <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
      <div class="w-full mt-[120px]">
        <div class="flex">
          <img loading="lazy" :src="rightArrow" alt="viajar a china" class="mr-[10px]" />
          <span class="text-[18px] leading-[18px] text-[#7F7F7F]">SI VIENE A CHINA</span>
        </div>
        <div class="text-[34px] leading-[34px] font-medium mt-[24px]">¿Cómo trabajamos?</div>
        <div class="mt-[50px] flex items-center">
          <div class="rounded-[12px] w-[635] h-[357px] overflow-hidden bg-white mr-[34px]">
            <video-you-tube
              :width="635"
              :height="357"
              :key="pageData.activatedWorkVideo?.id"
              :poster="pageData.activatedWorkVideo.poster"
              :youtubeId="pageData.activatedWorkVideo?.id"
              :title="pageData.activatedWorkVideo?.title"
              :titleCh="pageData.activatedWorkVideo?.titleCh"
            ></video-you-tube>
          </div>
          <n-space vertical :style="{ gap: '40px 0' }" class="ml-auto">
            <div
              v-for="item in workVideoData"
              :key="item.id"
              class="flex items-center cursor-pointer"
              @click="onPlayVideo(item)"
            >
              <img
                loading="lazy"
                :src="item.id === pageData.activatedWorkVideo?.id ? videoing : video"
                alt="viajar a china"
                class="mr-[10px]"
              />
              <div
                :class="item.id === pageData.activatedWorkVideo?.id ? '!text-[#e50113]' : ''"
                class="w-[450px] text-[20px] leading-[20px] text-medium"
              >
                {{ item.title }}
              </div>
            </div>
          </n-space>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[500px] mt-[94px] h-[50px]"
          >
            <span class="text-[18px] leading-[18px] px-[38px]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="bg-[#E50113] text-[#FFF] relative z-1">
      <div class="w-[1280px] mx-auto relative px-[38px] py-[80px]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[688px] absolute right-[38px] top-[-50px]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[40px] leading-[48px] font-medium">¿Por qué nosotros?</div>
        <div class="w-[50px] h-[3px] bg-[#fff] mt-[36px]"></div>
        <n-button
          size="large"
          color="#e50113"
          text-color="#fff"
          @click="onWhatsAppClick"
          class="mx-auto rounded-[500px] mt-[36px] h-[50px] whatsapp-btn"
        >
          <span class="text-[18px] leading-[18px] px-[12px]"> Comience a abastecerse ahora</span>
        </n-button>
        <div class="flex justify-between mt-[112px]">
          <div v-for="(item, index) in whyUsData" :key="index" class="w-[250px] h-[262px] border-b-1 border-[#FFF]">
            <div class="flex text-[30px] leading-[30px] font-medium">
              <img loading="lazy" :src="rightArrowWhite" alt="viajar a china" class="mr-[12px]" />
              0{{ index + 1 }}
            </div>
            <div class="min-h-[60px] text-[20px] leading-[20px] font-medium mt-[30px]" v-html="item.title"></div>
            <div class="text-[16px] leading-[20px] text-[#F2F2F2] mt-[20px]">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#F7F7F7] overflow-hidden">
      <div class="w-[1280px] h-[1079px] mx-auto relative flex flex-col">
        <div class="w-[1384px] h-[1384px] rounded-full bg-white absolute top-[-96px] right-[-133px]"></div>
        <div class="relative z-1 w-full">
          <div class="text-[34px] leading-[34px] font-medium text-center pt-[200px]">
            Conozca a las personas que eligieron chilat
          </div>
          <div class="w-[50px] h-[3px] bg-[#E50113] mt-[36px] mx-auto"></div>
          <div class="flex justify-between flex-wrap gap-x-[20px] gap-y-[44px] mt-[78px] px-[38px]">
            <div
              class="w-[388px] h-[218px] rounded-[4px] relative overflow-hidden"
              v-for="(video, index) in userVideoData"
              :key="video.id"
              @click="onOpenVideo(video)"
            >
              <n-image lazy preview-disabled :src="video.poster" class="img" object-fit="cover" />
              <img
                loading="lazy"
                :src="videoPlay"
                alt="viajar a china"
                class="w-[70px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-[1280px] mx-auto">
      <WhatsAppContact />
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import rightArrow from "@/assets/icons/viajar-a-china/rightArrow.svg";
import rightArrowWhite from "@/assets/icons/viajar-a-china/rightArrowWhite.svg";
import video from "@/assets/icons/como-trabajarnos/video.svg";
import videoing from "@/assets/icons/como-trabajarnos/videoing.svg";
import videoPlay from "@/assets/icons/viajar-a-china/videoPlay.svg";
import money from "@/assets/icons/viajar-a-china/money.svg";
import shopping from "@/assets/icons/viajar-a-china/shopping.svg";
import find from "@/assets/icons/viajar-a-china/find.svg";
import cooperation from "@/assets/icons/viajar-a-china/cooperation.svg";
import travelTips from "@/assets/icons/viajar-a-china/travelTips.png";
import accommodation from "@/assets/icons/viajar-a-china/accommodation.png";
import expertAdvice from "@/assets/icons/viajar-a-china/expertAdvice.png";
import interpreters from "@/assets/icons/viajar-a-china/interpreters.png";
import orderQualityControl from "@/assets/icons/viajar-a-china/orderQualityControl.png";
import logisticsAssistance from "@/assets/icons/viajar-a-china/logisticsAssistance.png";
import workVideoPoster1 from "@/assets/icons/viajar-a-china/workVideoPoster1.png";
import workVideoPoster2 from "@/assets/icons/viajar-a-china/workVideoPoster2.png";
import workVideoPoster3 from "@/assets/icons/viajar-a-china/workVideoPoster3.png";
import workVideoPoster4 from "@/assets/icons/viajar-a-china/workVideoPoster4.png";
import workVideoPoster5 from "@/assets/icons/viajar-a-china/workVideoPoster5.png";

import colombiaVideoPoster2 from "@/assets/icons/vip/colombia-video-poster2.jpg";
import argentinaVideoPoster3 from "@/assets/icons/vip/argentina-video-poster3.jpg";
import argentinaVideoPoster4 from "@/assets/icons/vip/argentina-video-poster4.jpg";
import argentinaVideoPoster5 from "@/assets/icons/vip/argentina-video-poster5.jpg";
import chileVideoPoster from "@/assets/icons/vip/chile-video-poster.jpg";
import hondurasVideoPoster from "@/assets/icons/vip/honduras-video-poster.jpg";

useHead({
  title: "Viajar a China - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/viajar-a-china/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/viajar-a-china/`,
    },
  ],
});

const videoModalRef = ref<any>(null);

const visitChinaData = [
  {
    imgUrl: money,
    title: "Precio competitivo",
    desc: "Los eficientes procesos de producción y las economías de escala de China permiten tener precios muy competitivos, pudiendo obtener los mejores márgenes de beneficio.",
  },
  {
    imgUrl: shopping,
    title: "Amplia gama de productos",
    desc: "Ya sea que esté buscando productos electrónicos, textiles, maquinaria o cualquier otro producto, China cuenta con una amplia gama y gran capacidad de producción de bienes de capital.",
  },
  {
    imgUrl: find,
    title: "Garantía de calidad",
    desc: "Inspeccione personalmente las fábricas y los procesos de producción para garantizar buena calidad y cumplir con sus estándares.",
  },
  {
    imgUrl: cooperation,
    title: "Construir relaciones comerciales",
    desc: "Las reuniones cara a cara con proveedores le ayudarán a establecer confianza y relaciones comerciales más sólidas, mejorando la comunicación y la cooperación.",
  },
];

const visitProcData = [
  {
    imgUrl: travelTips,
    title: "Consejos de viaje",
    descData: [
      "Antes de su viaje a China, investigaremos cada uno de los mercados mayoristas que desee visitar segun los requisitos de sus productos.",
      "De esta manera, priorizamoslos lugares que necesita paraasegurar el uso más productivode su tiempo.",
    ],
  },
  {
    imgUrl: accommodation,
    title: "Alojamiento",
    descData: [
      "Arreglos de alojamientoconfortable durante el viajepara asegurar que losparticipantes tengan unaestancia relajante y placentera.",
    ],
  },
  {
    imgUrl: expertAdvice,
    title: "Especialistas asesoramiento",
    descData: [
      "Expertos profesionales enasesoramiento que lesacompañan al grupo, brindandoorientación, respondiendopreguntas y ayudando con elproceso de asesoramientodurante todo el viaje.",
    ],
  },
  {
    imgUrl: interpreters,
    title: "Intérpretes",
    descData: [
      "Las barreras del idioma puedenser un desafio, pero la inclusiónde intérpretes de hablahispana, Garantiza unacomunicación fuida durante lasvisitas al mercado mayorista ylas reuniones con proveedores.",
    ],
  },
  {
    imgUrl: orderQualityControl,
    title: "Control de pedidos y calidad",
    descData: [
      "Realizar un seguimiento detodo el proceso de producciónpara garantizar que la entregay la calidad del productocumplan con sus estándares.",
    ],
  },
  {
    imgUrl: logisticsAssistance,
    title: "Asistencia Logistica",
    descData: [
      "En cuanto a los envos,brindamos asesoramiento entodo lo que tenga que ver conHetes, tramites aduaneros, etc.",
      "Nos ocupamos de todos losaspectos logisticos paraayudarle a navegar lascomplejidades del comerciointernacional.",
    ],
  },
];

const workVideoData = [
  {
    id: "b7X9D3BLvMc",
    poster: workVideoPoster1,
    title: "Emitir carta de invitación/reserva de hotel",
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: workVideoPoster2,
    title: "Recibir en el aeropuerto y visita al mercado",
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "wWtOhkPDg5A",
    poster: workVideoPoster3,
    title: "Realizar pedidos y seguimiento de producción",
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "GN6F6oLssUw",
    poster: workVideoPoster4,
    title: "Recibir los productos y hacer una inspección de calidad",
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: workVideoPoster5,
    title: "Cargar el contenedor y organizar los documentos de envío",
    titleCh: "5.装载集装箱并组织运输",
  },
];

const userVideoData = [
  {
    id: "bdfbqoK-Z3w",
    poster: colombiaVideoPoster2,
    titleCh: "1.我们的客户对我们的看法15",
  },
  {
    id: "h7-0oqtZGTU",
    poster: argentinaVideoPoster3,
    titleCh: "2.我们的客户对我们的看法16",
  },
  {
    id: "pzKmAInWpRo",
    poster: argentinaVideoPoster4,
    titleCh: "3.我们的客户对我们的看法17",
  },
  {
    id: "meWjJ7gWg8U",
    poster: argentinaVideoPoster5,
    titleCh: "4.我们的客户对我们的看法18",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: chileVideoPoster,
    titleCh: "5.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: hondurasVideoPoster,
    titleCh: "6.我们的客户对我们的看法6",
  },
];

const whyUsData = [
  {
    title: "CHILAT es una marca muy conocida en el mercado de América Latina",
    desc: "Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
  },
  {
    title: "Equipo <br/>experimentado",
    desc: "Contamos con más de 50 empleados comerciales de habla hispana, el 80% de ellos con más de 3 años de servicio, lo que nos brinda una amplia experiencia.",
  },
  {
    title: "Abundantes recursos de las fabricas",
    desc: "Con más de 2000 proveedores principales, garantizamos una perfecta coordinación de la producción.",
  },
  {
    title: "Equipo de aliados en múltiples países en América Latina",
    desc: "Contamos con agencias asociadas en varios países de América Latina que pueden brindarle soluciones localizadas.",
  },
];

const pageData = reactive(<any>{
  activatedWorkVideo: workVideoData[0],
});

function scrollToNav(dom: any) {
  if (!dom) return;
  const element = document.getElementById(dom);
  if (!element) return;
  const elementRect = element.getBoundingClientRect();
  const offsetPosition = elementRect.top + window.scrollY;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

function onPlayVideo(val: any) {
  pageData.activatedWorkVideo = val;
}

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  min-width: 1280px;
  height: auto;
  min-height: 100vh;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 584px;
  background-size: cover;
  color: #fff;
  background-image: url("@/assets/icons/viajar-a-china/svipVisitBg.png");
  background-repeat: no-repeat;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(21, 27, 35, 0.25);
    z-index: 1;
  }
}
.side-affix {
  position: fixed;
  right: 28px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}
.whatsapp-btn {
  border: 1px solid #fff;
}
</style>
