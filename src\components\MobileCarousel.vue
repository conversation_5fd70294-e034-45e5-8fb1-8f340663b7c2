<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from "vue";

const props = defineProps({
  images: Array, // 图片数组
  direction: {
    type: String,
    default: "left", // 滚动方向，可选 "left" 或 "right"
  },
  speed: {
    type: Number,
    default: 50, // 滚动速度（像素/秒），数值越大速度越快
  },
  autoplay: {
    type: Boolean,
    default: false, // 默认不自动播放
  },
  gap: {
    type: Number,
    default: 20, // 图片之间的间距
  },
  imageWidth: {
    type: String,
    default: "auto", // 图片宽度，可以是具体像素值或百分比
  },
  imageHeight: {
    type: String,
    default: "238px", // 默认图片高度
  },
  autoPlayWhenVisible: {
    type: Boolean,
    default: false, // 默认不启用视口检测自动播放
  },
});

const container = ref(null);
const scrollContent = ref(null);

// 视口检测相关状态
const isVisible = ref(false);
const observer = ref(null);

// 实际内容宽度
const contentWidth = ref(0);

// 计算动画持续时间 - 基于实际像素宽度和速度
const animationDuration = computed(() => {
  if (contentWidth.value === 0) {
    // 如果还没有测量到宽度，使用默认值
    return "20s";
  }

  // 计算一个完整循环需要移动的距离（一组图片的宽度 + gap）
  const moveDistance = contentWidth.value / 2; // 因为有两组相同的图片

  // 根据速度（像素/秒）计算时间
  const duration = moveDistance / props.speed;

  return `${Math.max(duration, 0.5)}s`; // 最小0.5秒，避免过快
});

// 测量内容宽度
const measureContentWidth = async () => {
  await nextTick();
  if (scrollContent.value) {
    const firstGroup = scrollContent.value.querySelector(".image-group");
    if (firstGroup) {
      contentWidth.value = firstGroup.scrollWidth;
    }
  }
};

// 计算动画方向
const animationDirection = computed(() => {
  return props.direction === "left" ? "scroll-left" : "scroll-right";
});

// 计算是否应该播放动画
const shouldPlay = computed(() => {
  if (props.autoPlayWhenVisible) {
    return props.autoplay && isVisible.value;
  }
  return props.autoplay;
});

// 初始化 Intersection Observer
const initIntersectionObserver = () => {
  if (!props.autoPlayWhenVisible || !container.value) return;

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        isVisible.value = entry.isIntersecting;
      });
    },
    {
      threshold: 0.1, // 当组件10%可见时触发
      rootMargin: "100px", // 提前100px触发
    }
  );

  observer.value.observe(container.value);
};

// 清理 Intersection Observer
const cleanupObserver = () => {
  if (observer.value) {
    observer.value.disconnect();
    observer.value = null;
  }
};

onMounted(() => {
  initIntersectionObserver();
  measureContentWidth();

  // 监听图片加载完成，重新测量宽度
  if (scrollContent.value) {
    const images = scrollContent.value.querySelectorAll("img");
    let loadedCount = 0;
    const totalImages = images.length;

    const onImageLoad = () => {
      loadedCount++;
      if (loadedCount === totalImages) {
        // 所有图片加载完成，重新测量宽度
        measureContentWidth();
      }
    };

    images.forEach((img) => {
      if (img.complete) {
        onImageLoad();
      } else {
        img.addEventListener("load", onImageLoad);
        img.addEventListener("error", onImageLoad); // 即使加载失败也要计数
      }
    });
  }
});

onUnmounted(() => {
  cleanupObserver();
});
</script>

<template>
  <div ref="container" class="carousel-container">
    <div
      ref="scrollContent"
      class="carousel-content"
      :class="{
        'auto-scroll': shouldPlay,
        [animationDirection]: shouldPlay,
      }"
      :style="{
        '--gap': `${gap}px`,
        '--speed': animationDuration,
      }"
    >
      <!-- 第一组图片 -->
      <div class="image-group">
        <img
          v-for="(image, index) in images"
          :key="`group1-${index}`"
          :src="image"
          alt="carousel-image"
          class="carousel-image"
          :style="{ width: imageWidth, height: imageHeight }"
          draggable="false"
          loading="lazy"
        />
      </div>
      <!-- 第二组图片（重复内容，用于无缝循环） -->
      <div class="image-group" aria-hidden="true">
        <img
          v-for="(image, index) in images"
          :key="`group2-${index}`"
          :src="image"
          alt="carousel-image"
          class="carousel-image"
          :style="{ width: imageWidth, height: imageHeight }"
          draggable="false"
          loading="lazy"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.carousel-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  user-select: none;
}

.carousel-content {
  display: flex;
  overflow: hidden;
  user-select: none;
  gap: var(--gap);
}

.image-group {
  flex-shrink: 0;
  display: flex;
  justify-content: space-around;
  min-width: 100%;
  gap: var(--gap);
}

.carousel-image {
  flex-shrink: 0;
  object-fit: cover;
  border-radius: 8px;
}

/* CSS动画实现无感滚动 */
.auto-scroll.scroll-left .image-group {
  animation: scroll-left var(--speed) linear infinite;
}

.auto-scroll.scroll-right .image-group {
  animation: scroll-right var(--speed) linear infinite;
}

@keyframes scroll-left {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes scroll-right {
  from {
    transform: translateX(calc(-100% - var(--gap)));
  }
  to {
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (prefers-reduced-motion: reduce) {
  .auto-scroll {
    animation: none !important;
  }
}
</style>
