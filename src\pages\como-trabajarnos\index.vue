<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[24px] py-[50px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-medium mt-[40px]">Cómo trabajarnos</div>
    </div>
    <div class="pt-[120px] pb-[150px] px-[38px]">
      <div class="text-[44px] leading-[52px] font-medium w-[588px]">¿Cómo Chilat le ayuda a importar de China?</div>
      <div class="my-[50px] flex">
        <img :src="arrowRightRed" alt="como trabajarnos" class="w-[14px] mr-[10px]" loading="lazy" />
        <div class="text-[20px] leading-[20px] text-[#7F7F7F]">SI VIENE A CHINA</div>
      </div>
      <div class="flex items-center w-full">
        <div class="w-[635px] h-[357px] mr-[40px] pr-[40px] border-r-1 border-[#F2F2F2] flex-shrink-0">
          <div class="rounded-[20px] w-[635px] h-[357px] overflow-hidden bg-white video-container">
            <transition name="video-transition" mode="out-in">
              <div :key="pageData.activeService?.id" class="w-full h-full">
                <video-you-tube
                  :autoplay="pageData.isUserSwitched"
                  :width="635"
                  :height="357"
                  :poster="pageData.activeService?.poster"
                  :youtubeId="pageData.activeService?.id"
                  :title="pageData.activeService?.title"
                  :titleCh="pageData.activeService?.titleCh"
                ></video-you-tube>
              </div>
            </transition>
          </div>
        </div>

        <n-space vertical :style="{ gap: '40px 0' }">
          <div
            v-for="item in serviceProcessChina"
            :key="item.id"
            class="flex items-center cursor-pointer"
            @click="onPlayVideo(item)"
          >
            <img
              :src="item.id === pageData.activeService?.id ? videoing : video"
              alt="como trabajarnos"
              class="mr-[10px]"
              loading="lazy"
            />
            <div
              :class="item.id === pageData.activeService?.id ? 'text-[24px] leading-[24px] !text-[#e50113]' : ''"
              class="w-[450px] text-[20px] leading-[20px] text-medium"
            >
              {{ item.title }}
            </div>
          </div>
        </n-space>
      </div>
      <!-- <div class="my-[50px] flex justify-end">
        <img
          :src="arrowRightRed"
          alt="como trabajarnos"
          class="w-[14px] mr-[10px]"
          loading="lazy"
        />
        <div class="text-[20px] leading-[20px] text-[#7F7F7F]">
          SI NO VIENE A CHINA
        </div>
      </div>
      <div class="flex items-center">
        <n-space vertical :style="{ gap: '40px 0' }">
          <div
            v-for="item in serviceProcessNoChina"
            :key="item.id"
            class="flex items-center cursor-pointer"
            @click="onPlayVideo(item)"
          >
            <img
              :src="
                item.disabled
                  ? videoDisabled
                  : item.id === pageData.activeServiceNoChina?.id
                  ? videoing
                  : video
              "
              alt="como trabajarnos"
              class="mr-[10px]"
              loading="lazy"
            />
            <div
              :class="
                item.disabled
                  ? 'text-[#7F7F7F] cursor-default'
                  : item.id === pageData.activeServiceNoChina?.id
                  ? 'text-[24px] leading-[24px] !text-[#e50113]'
                  : ''
              "
              class="w-[450px] text-[20px] leading-[20px] text-medium"
            >
              {{ item.title }}
            </div>
          </div>
        </n-space>
        <div class="ml-[40px] pl-[40px] border-l-1 border-[#F2F2F2]">
          <div
            class="rounded-[20px] w-[635] h-[357px] overflow-hidden bg-white"
          >
            <video-you-tube
              :autoplay="pageData.isUserSwitched"
              :width="635"
              :height="357"
              :key="pageData.activeServiceNoChina?.id"
              :poster="pageData.activeServiceNoChina.poster"
              :youtubeId="pageData.activeServiceNoChina?.id"
              :title="pageData.activeServiceNoChina?.title"
              :titleCh="pageData.activeServiceNoChina?.titleCh"
            ></video-you-tube>
          </div>
        </div>
      </div> -->
    </div>
    <div
      class="rounded-[20px] w-full h-[264px] text-[#fff] pt-[50px] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-bold">¿Por qué nuestros servicios pueden ayudarle?</div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[204px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Leer más</span>
        <img loading="lazy" alt="como trabajarnos" class="arrow-icon" src="@/assets/icons/quienes-somos/arrow-line.svg" />
      </n-button>
    </div>
    <WhatsAppContact :showWhatsAppCtaBanner="false"></WhatsAppContact>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import arrowRightRed from "@/assets/icons/como-trabajarnos/arrow-right-red.svg";
import servicePoster1 from "@/assets/icons/como-trabajarnos/servicePoster1.png";
import servicePoster2 from "@/assets/icons/como-trabajarnos/servicePoster2.png";
import servicePoster3 from "@/assets/icons/como-trabajarnos/servicePoster3.png";
import servicePoster4 from "@/assets/icons/como-trabajarnos/servicePoster4.png";
import servicePoster5 from "@/assets/icons/como-trabajarnos/servicePoster5.png";
import servicePoster6 from "@/assets/icons/como-trabajarnos/servicePoster6.png";
import redBg from "@/assets/icons/quienes-somos/red-bg.png";
import videoing from "@/assets/icons/como-trabajarnos/videoing.svg";
import video from "@/assets/icons/como-trabajarnos/video.svg";
// import videoDisabled from "@/assets/icons/como-trabajarnos/video-disabled.svg";

useHead({
  title: "Cómo trabajarnos - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/como-trabajarnos/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/como-trabajarnos/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { text: "Nosotros" },
  { link: "/como-trabajarnos", text: "Cómo trabajarnos" },
];

const serviceProcessChina = [
  {
    id: "b7X9D3BLvMc",
    poster: servicePoster1,
    title: "Emitir carta de invitación/reserva de hotel",
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: servicePoster2,
    title: "Recibir en el aeropuerto y visita al mercado",
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "GN6F6oLssUw",
    poster: servicePoster3,
    title: "Realizar pedidos y seguimiento de producción",
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "wWtOhkPDg5A",
    poster: servicePoster4,
    title: "Recibir los productos y hacer una inspección de calidad",
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: servicePoster5,
    title: "Cargar el contenedor y organizar los documentos de envío",
    titleCh: "5.装载集装箱并组织运输",
  },
];

const serviceProcessNoChina = [
  {
    id: "94ABcP_GQFs",
    poster: servicePoster6,
    title: "Obtención de productos (gratis) y confirmación de muestra",
    titleCh: "1.获得产品（免费）和样品确认",
  },
  {
    id: "VCgjAWzHB1A",
    disabled: true,
    poster: servicePoster2,
    title: "El mismo proceso si vienes a China",
    titleCh: "2.如果你来中国，同样的过程",
  },
];

const pageData = reactive(<any>{
  activeService: serviceProcessChina[0],
  activeServiceNoChina: serviceProcessNoChina[0],
  isUserSwitched: false,
});

function onPlayVideo(val: any) {
  if (val.disabled) return;
  // 检查是哪个列表的视频被点击
  const isServiceChina = serviceProcessChina.some((item) => item.id === val.id);

  if (isServiceChina) {
    pageData.activeService = val;
    pageData.isUserSwitched = true;
  }
}
</script>
<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/como-trabajarnos/header-bg.png");
  background-repeat: no-repeat;
}

.video-transition-enter-active {
  transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.video-transition-leave-active {
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

.video-transition-enter-from {
  opacity: 0;
}

.video-transition-leave-to {
  opacity: 0;
  transform: scale(0.96);
}

.video-container {
  position: relative;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  will-change: opacity;
}
</style>
