syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";

// 翻译器
service Translator {
  // 翻译文字
  rpc translate(common.StringsParam) returns (common.MapResult);

  // 翻译为中文
  rpc transToZh(common.StringsParam) returns (common.MapResult);

  // 将商品信息（文本），翻译为西语
  rpc transGoodsToEs(common.StringsParam) returns (common.MapResult);

  // 翻译图片
  rpc translateImage(common.StringsParam) returns (common.MapResult);
}