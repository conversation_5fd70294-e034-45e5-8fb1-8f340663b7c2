syntax = "proto3";
package chilat.trade;

option java_package = "com.chilat.rpc.trade.model";

import "chilat/trade/purchase_common.proto";
import "chilat/commodity/model/goods_info_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

message PurchasePageResp {
  common.Result result = 1;
  PurchaseDataModel data = 2;
}

message PurchaseOrderPageResp {
  common.Result result = 1;
  PurchaseOrderDataModel data = 2;
}

message PurchaseOrderResp {
  common.Result result = 1;
  PurchaseOrderModel data = 2;
}

message PurchasePayLinkResp {
  common.Result result = 1;
  repeated PurchasePayLinkModel data = 2;
}

message PurchaseCancelResp {
  common.Result result = 1;
  repeated PurchaseCancelModel data = 2;
}

message PurchaseCancelModel {
  string orderId = 1; // 订单ID
  string specInfo = 2; // 规格信息
  bool success = 3; // 是否成功
  string errorMessage = 4; // 失败原因
}

message PurchasePayLinkModel {
  repeated PurchaseOrderLineModel orders = 1; // 订单信息
  string payLink = 2; // 支付链接
  string errorMessage = 3; // 失败原因
}

message PurchaseOrderModel {
  string purchaseId = 1; // 采购单ID
  string subAccountId = 4; //子账号ID
  PurchaseReceiverModel receiver = 2; // 收件人
  repeated PurchaseOrderSkuModel goods = 3; // 商品信息
  string userDefinedOrderNo = 10; //自定义订单编号
  string userDefinedCustomerNo = 11; //自定义客户编号
  string salesManId = 12; //业务员Id
  string salesManName = 13; //业务员名称
  string salesOrderNo = 14; //2.0订单号
}

message PurchaseDataModel {
  common.Page page = 1;
  repeated PurchaseModel data = 2; // 采购单列表
  int32 unOrderedCount = 3; // 待下单数量
  int32 unPaidCount = 4; // 待付款数量
  int32 waitShippingCount = 5; // 待发货数量
  int32 waitReceiveCount = 6; // 待收货数量
  int32 finishedCount = 7; // 已完成数量
  int32 canceledCount = 8; // 已取消数量
}

message PurchaseModel {
  string id = 1;
  string purchaseNo = 2; // 采购单号
  PurchaseOrderState orderState = 3; // 采购单状态
  string orderStateDesc = 4; // 采购单状态描述
  repeated InnerPurchaseModel innerPurchases = 5; // 内采单列表
  int32 totalCount = 6; // 商品总数
  double totalPrice = 7; // 采购总价
  string purchaserName = 8; // 采购员
  string subAccountName = 14; //子账号名称
  string remark = 9; // 备注
  string buyerRemark = 10; // 下单备注
  string address = 11; // 地址
  string coperator = 12; // 创建人
  int64 cdate = 13; // 创建时间
  string salesOrderNo = 20; //2.0订单号
  int32 totalSkuCount = 30; //sku总款数
  string userDefinedOrderNo = 31; //自定义订单编号
  string userDefinedCustomerNo = 32; //自定义客户编号
  double orderTotalPrice = 33; // 订单总费用(已下单部分)
  double paidTotalPrice = 34; //实际支付金额
  string salesManId = 35; // 业务员ID
  string salesManName = 36; // 业务员名称
  double waitPayTotalPrice = 40; //待支付金额
}

message InnerPurchaseModel {
  string id = 1;
  string innerPurchaseNo = 2; // 内部采购单号
  PurchaseOrderState orderState = 4; // 采购单状态
  string orderStateDesc = 5; // 采购单状态描述
  int32 totalCount = 6; // 商品总数
  double totalPrice = 7; // 采购总价
  string salesOrderNo = 20; //2.0订单号
  int32 totalSkuCount = 30; //sku总款数
  double orderTotalPrice = 33; // 订单总费用(已下单部分)
  double paidTotalPrice = 34; //实际支付金额
  string sellerName = 35; //供应商名称
  double waitPayTotalPrice = 40; //待支付金额
}

message PurchaseOrderDataModel {
  common.Page page = 1;
  repeated PurchaseOrderLineModel orderData = 2;
  repeated PurchaseOrderSkuModel skuData = 3;
  int32 waitOrderedCount = 4; // 待下单数量
  int32 orderedCount = 5; // 已下单数量
  int32 canceledCount = 6; // 取消数量
  PurchaseModel purchaseData = 7; // 采购单数据
  InnerPurchaseModel innerPurchaseData = 8; // 内采单数据
}

message PurchaseOrderLineModel {
  string orderId = 1; // 订单ID
  string orderNo = 2; // 内部订单号
  string outOrderNo = 3; // 1688订单号
  PurchasePayState payState = 4; // 1688支付状态
  string payStateDesc = 5; // 1688支付状态描述
  PurchaseOrderState orderState = 6; // 1688订单状态
  string orderStateDesc = 7; // 1688订单状态描述
  int32 skuCount = 8; // sku数量
  int32 totalCount = 9; // 总数量
  int64 orderTime = 10; // 下单时间
  string address = 11; // 收货地址
  repeated PurchaseOrderSkuModel skuList = 12; // sku列表
  string warehouseName = 13; // 仓库名称
  string purchaserName = 14; // 采购员
  string subAccountName = 22; //子账号名称
  string remark = 15; // 备注
  string buyerRemark = 16; // 下单备注
  double discountFee = 17; // 折扣金额
  double sumCarriage = 18; // 运费
  double sumPayment = 19; // 总金额
  double sumPaymentNoCarriage = 20; // 订单金额(不含运费)
  double additionalFee = 21; // 附加费用
}

message PurchaseOrderSkuModel {
  string goodsId = 1; // 商品ID (采购单商品的id)
  string imageUrl = 2; // sku图片
  string sourceGoodsId = 3; // 1688商品ID
  string goodsName = 4; // 1688商品名称
  string goodsNameCn = 5; // 1688商品中文名称
  repeated commodity.GoodsExtendItem specList = 6; // 规格
  string sourceSkuId = 7; // 1688skuID
  string sourceSpecId = 8; // 1688规格ID
  int32 count = 9; // 数量
  double price = 10; // 单价
  string address = 11; // 收货地址
  string sellerId = 12; // 卖家标识
  string extra = 13; // 额外信息
  double amount = 14; // 下单总金额
  double finalUnitPrice = 15; // 最终单价
  double totalDiscountFee = 16; // 优惠总金额
  string innerSkuId = 20 ; // 内部skuId
  string innerGoodsId = 21; // 内部goodsId
  string innerOrderLineNo = 22 ; // 2.0订单行号
  string logisticsInfo = 23; //物流信息
  string innerGoodsNameCn = 30; //2.0订单商品中文名称
  string innerImageUrl = 31; //2.0订单sku图片
  string innerSkuNo = 32; //2.0订单skuNo
  repeated commodity.GoodsExtendItem innerSpecList = 33; // 2.0订单sku中文规格
}

message PurchaseReceiverModel {
  string warehouseId = 1; // 仓库ID
  string purchaserId = 2; // 采购员ID
  string purchaserName = 3; // 采购员名称
  string remark = 4; // 备注
  string receiverName = 5; // 收件人
  string phone = 6; // 手机
  string postcode = 7; // 邮编
  string province = 8; // 省
  string city = 9; // 市
  string district = 10; // 区
  string town = 11; // 镇
  string streetAddress = 12; // 街道地址
  string buyerRemark = 13; // 下单备注
}