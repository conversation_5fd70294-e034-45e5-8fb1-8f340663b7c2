<template>
  <div class="bg-white px-[0.4rem] mb-[2.4rem]">
    <!-- 面包屑导航 -->
    <div class="mt-[0.6rem]">
      <n-breadcrumb separator=">">
        <n-breadcrumb-item>
          <icon-card
            size="24"
            color="#7F7F7F"
            class="mr-[-0.08rem] cursor-pointer"
            name="ic:sharp-home"
            @click="onNavigateHome"
          >
          </icon-card>
        </n-breadcrumb-item>
        <n-breadcrumb-item>
          <div class="text-[#7F7F7F] text-[0.28rem] font-400 leading-[0.28rem]">
            <a href="/blog"> Blog </a>
          </div>
        </n-breadcrumb-item>
        <n-breadcrumb-item>
          <div class="text-[#333] text-[0.28rem] font-500 leading-[0.28rem]">
            {{ pageData.detail?.title }}
          </div>
        </n-breadcrumb-item>
      </n-breadcrumb>
    </div>
    <!-- 标签 -->
    <div class="flex flex-wrap gap-1 mt-[0.64rem]" v-if="pageData.detail?.articleCategories?.length > 0">
      <div v-for="(category, index) in pageData.detail?.articleCategories" :key="index">
        <n-button
          round
          strong
          secondary
          type="tertiary"
          :bordered="false"
          @click="onCategoryClick(index)"
          class="bg-[#F2F2F2] text-[#7F7F7F] font-400 text-[0.24rem] leading-[0.24rem]"
        >
          {{ category.name }}
        </n-button>
      </div>
    </div>
    <!-- 标题 -->
    <div class="flex flex-wrap mt-[0.52rem]">
      <div class="text-[#333] text-[0.56rem] font-500 leading-[0.64rem]">
        {{ pageData.detail?.title }}
      </div>
    </div>
    <!-- 发布日期 -->
    <div class="flex flex-wrap gap-4 mt-[0.52rem]">
      <div
        class="text-[#7F7F7F] text-[0.28rem] font-400 leading-[0.28rem] italic cursor-pointer"
        @click="onPublishDateClick"
      >
        <span>Etpublicado en</span>
        <span class="text-[#4290F7] ml-[0.08rem]">{{
          timeFormatByZone(pageData.detail?.udate, false, false, false)
        }}</span>
      </div>
    </div>
    <!-- 文章内容 -->
    <div class="flex flex-wrap gap-4 mt-[0.64rem]">
      <div v-html="pageData.detail?.content" class="text-[#333] text-[0.36rem] font-400 leading-[0.54rem]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

const route = useRoute();
const pageData = reactive<any>({
  detail: null,
});

await onPageData();
useHead({
  title: `${pageData.detail?.title} - Chilat`,
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/blog/${route.params.id}`,
    },
  ],
  meta: [
    {
      name: "description",
      content: pageData.detail?.title,
    },
  ],
});

async function onPageData() {
  const res: any = await useArticleDetail({
    id: route.params.id,
    articleCode: route.params.id,
  });
  if (res?.result?.code === 200) {
    pageData.detail = res.data;
  }
}

function onNavigateHome() {
  navigateToPage("/", {}, false);
}

function onCategoryClick(index: number) {
  const category = pageData.detail?.articleCategories[index];
  navigateToPage("/blog", { cateId: category.id }, false);
}

function onPublishDateClick() {
  navigateToPage("/blog", { date: pageData.detail?.udate }, false);
}
</script>

<style scoped lang="scss"></style>
