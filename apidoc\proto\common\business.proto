syntax = "proto3";
package common;

option java_package = "com.chilat.rpc.common.business";

import "common.proto";

/*------------------------------ 业务相关公共参数 ------------------------------*/

// SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同）
message SkuStepRange {
    int32 start = 1; // 起始件数（第一个阶梯的start，总是从1开始）
    int32 end = 2; // 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1）
}

// SKU阶梯价格
message SkuStepPrice {
    int32 start = 1; // 起始件数（第一个阶梯的start，总是从1开始）
    int32 end = 2; // 结束件数（-1表示无穷大；最后一个阶梯价的end，总是-1）
    double price = 3; // 阶梯价（最小售卖单位的价格，即商品价格单位对应的价格；第一个阶梯的salePrice总是与sku中的salePrice相同）
    double insideOnePrice = 4; // 内含单件商品的价格（与包装含量相关；chilat2.0中的包装含量总是1，因此insideOnePrice总是与stepPrice相等）
}

// Seo标签数据
message SeoData {
    string title = 10; //html 网页标题
    string keyword = 20; //meta 网页关键字
    string description = 30; //meta 网页描述
    string headFirstScript = 110; //若存在，插入到 head 标签开始后的位置
    string headLastScript = 120; //若存在，插入到 head 标签结束前的位置
    string bodyFirstContent = 130; //若存在，插入到 body 标签开始后的位置
    string bodyLastContent = 140; //若存在，插入到 body 标签结束前的位置
    repeated NameValueModel responseHeaders = 150; //若存在，在 response 响应头部输出: res.setHeader(name, vale)
}

// 排序字段
message SortField {
    string fieldName = 1; //排序字段名（后端提供字段名；英文大小写不敏感）
    string sortType = 2; //排序类型（可选值：ASC升序，DESC降序；英文大小写不敏感）
}

// 访问设备类型
enum VisitDeviceType {
    VISIT_DEVICE_TYPE_UNSPECIFIED = 0;
    VISIT_DEVICE_TYPE_PC = 1;
    VISIT_DEVICE_TYPE_H5 = 2;
}

enum CurrencyType {
    CURRENCY_TYPE_UNSPECIFIED = 0;
    CURRENCY_TYPE_CNY = 10; //人民币
    CURRENCY_TYPE_USD = 20; //美元
    CURRENCY_TYPE_ARS = 30; //阿根廷比索
    CURRENCY_TYPE_BOB = 40; //玻利维亚诺
    CURRENCY_TYPE_CLP = 50; //智利比索
    CURRENCY_TYPE_COP = 60; //哥伦比亚比索
    CURRENCY_TYPE_CRC = 70; //哥斯达黎加科朗
    CURRENCY_TYPE_MXN = 80; //墨西哥比索
    CURRENCY_TYPE_PEN = 90; //秘鲁索尔

}

enum AddressLabel {
    ADDRESS_LABEL_HOME = 0; //家
    ADDRESS_LABEL_COMPANY = 1; //公司
}

enum TimeInterval {
    TIME_INTERVAL_YESTERDAY = 0;
    TIME_INTERVAL_TODAY = 1;
    TIME_INTERVAL_THREE_DAY = 2;
    TIME_INTERVAL_WEEK = 3;
    TIME_INTERVAL_MONTH = 4;
    TIME_INTERVAL_QUARTER = 5;
}

//报价模式
enum QuotationMode {
    QUOTATION_MODE_DDP = 0;
    QUOTATION_MODE_EXW = 1;
    QUOTATION_MODE_FOB = 2;
    QUOTATION_MODE_LCL = 3;
    QUOTATION_MODE_CIF = 4;
}

//支付模式
enum PayMode {
    PAY_MODE_ALL = 0; //一次性支付
    PAY_MODE_PART = 1; //分开支付
}

//线上或者线下支付
enum PayType {
    ONLINE_PAY_ONLINE = 0; //线上支付
    ONLINE_PAY_OFFLINE = 1; //线下支付
}

//线上或者线下支付
enum FeeType {
    ALL_FEE = 0; //全部
    DOMESTIC_FEE = 1; //国内费用
    OVERSEAS_FEE = 2; //国外费用
}

//订单类型
//状态： -100.审核前取消， 0.待审核 100.审核通过 200.部分支付 300.已支付产品成本 400.三方下单成功 500.三放已发货 600.已收货 700.已填写国际运费 800.支付国际运费(代发货) 900.已发货 1000.清关报关. 1100. 客户已签收 1200.审核后取消 1300.用户已取消
enum OrderStatus {
    STATUS_ALL = 0;
    CANCEL_BEFORE_AUDIT = 1; // 1.审核前取消
    WAITING_APPROVING = 2; //0.待审核
    APP_APPROVED = 3; //100.审核通过
    PARTIAL_PAYMENT = 4;// 200.部分支付
    PAID_PRODUCT = 5; //300.待采购
    THIRD_PARTY_ORDER_SUCCESS = 6; // 待工厂发货
    THIRD_PARTY_SHIPPED = 7; // 待仓库收货
    RECEIVED = 8; // 待客户验货
    FILLED_IN_INTERNATIONAL_FREIGHT = 9; // 待支付国际费用
    PAY_INTERNATIONAL_FREIGHT = 10; // 待仓库发货
    SHIPPED = 11; // 国际运输中
    CUSTOMS_DECLARATION = 12; // 当地派送中
    USER_SIGNED = 13; // 客户已签收
    CANCEL_AFTER_AUDIT = 14; // 2.审核后取消
    USER_CANCEL = 15; // 3.用户已取消
    WAITING_COMPUTE_INTERNATIONAL_FEE = 16; //生成发货单，待计算国际费用
    PURCHASE_SAVE = 51; //301.待采购下单
    PURCHASE_ORDER = 52; //302.待采购支付
    CUSTOMS_CLEARING = 60; // 901 清关中
}

//账单类型
enum SalesOrderBillType {
    ALL_BILL = 0;
    DOMESTIC_BILL= 1; // 100.国内账单
    OVERSEAS_BILL = 2; // 200.国际账单
}
//账单状态
enum SalesOrderBillStatus {
    BILL_STATUS_ALL = 0;
    BILL_CANCEL = 1; //-100.取消
    BILL_WAITING_PAYMENT = 2;//0.待支付
    BILL_PARTIAL_PAYMENT = 3;// 100.部分支付
    BILL_ALREADY_PAID = 4; //200.已支付
}

//操作类型
enum SalesOrderOperationType {
    OP_ALL = 0;
    OP_CANCEL = 1; // -100.取消
    OP_CREATE_ORDER = 2; // 0.创单
    OP_APPROVED = 3; // 100.审核通过
    OP_PAYMENT = 4;// 200.支付
    OP_THIRD_PARTY_ORDER = 5; // 400.第三方下单
    OP_THIRD_PARTY_SHIPPED = 6; // 500.第三方发货
    OP_RECEIVED = 7; // 600.收货
    OP_FILL_IN_INTERNATIONAL_FREIGHT = 8; // 700.填写国际运费
    OP_PAY_INTERNATIONAL_FREIGHT = 9; // 800.支付国际运费
    OP_SHIPPED = 10; // 900.发货
    OP_CUSTOMS_DECLARATION = 11; // 1000.报关
    OP_SIGNED = 12;// 1100.客户已签收
    OP_CANCEL_AFTER_AUDIT = 13; // -200
    OP_USER_CANCEL = 14; //-300
    OP_USER_NOTICE = 15; //客户通知
    OP_ADMIN_MODIFY_ORDER_STATUS = 16; // admin修改订单状态
}

//订单取消类型
enum SalesOrderCancelType {
    SO_NON_CANCEL = 0; // 0.非取消
    SO_CANCEL_BEFORE_AUDIT = 1; // 1.审核前取消
    SO_CANCEL_AFTER_AUDIT = 2; // 2.审核后取消
    SO_USER_CANCEL = 3; // 3.用户已取消
}

//字典code
enum DictCode {
    USER_EDIT_PWD = 0; //用户修改密码
    TRANSPORT = 1; //运输方式
}

enum SoHandleUserType {
    SYS = 0; //系统
    CONSUMER = 1; //消费者
    CUSTOMER = 2; // 客服
}

enum PayStatusEnum {
    INIT = 0; //未支付
    PAYING = 10; //已发起第三方支付,支付中
    PAID_SUCCESS = 20; //支付成功
    PAID_FAIL = 30; //支付失败
}

enum MallOrderStatus {
    MALL_STATUS_ALL = 0; //查询时用于查询全部状态
    MALL_WAITING_APPROVING = 1; //待审核----仅用于admin端预览
    MALL_WAIT_PAY_PRODUCT = 10; // 支付产品成本
    MALL_WAIT_PAY_ALL_FEE = 20; //支付订单费用
    MALL_WAIT_CAL_INTER_FEE = 30;//待计算国际费用
    MALL_WAIT_PAY_INTER_FEE = 40; // 支付国际费用
    MALL_PURCHASING = 50; // 采购
    MALL_WAIT_SEND_OUT = 60;//待发货
    MALL_TRANSPORTING = 70; // 国际运输
    MALL_CUSTOMS_CLEARING = 71; //清关中
    MALL_DELIVERING = 80; // 派送
    MALL_USER_SIGNED = 90; // 签收
    MALL_CANCELED = 100; // 已取消
}

enum VerifyMailSceneEnum {
    REGISTER = 0; //注册
    FIRST_GOODS_LOOKING = 1; //首次询盘
    INVITE_FRIEND = 2; //邀请好友
    MY_COUPON_LIST = 3 ; //个人中心-我的优惠券列表
}

enum VerifyMailResultEnum {
    SUCCESS = 0; // 验证成功
    EXPIRED = 1; // 验证链接已失效
    ALREADY_VERIFIED = 2; // 已验证过
}