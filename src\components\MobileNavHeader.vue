<template>
  <div class="mobile-nav-container">
    <!-- 导航栏头部 -->
    <n-layout-header class="mobile-header">
      <div class="header-content">
        <a href="/h5">
          <img
            src="@/assets/icons/common/logo.png"
            alt="Chilat Logo"
            class="logo"
            loading="lazy"
          />
        </a>
        <div @click="toggleDrawer">
          <img
            v-if="!showDrawer"
            :src="moreMenu"
            alt="menu"
            class="w-[0.4rem]"
            loading="lazy"
          />
          <img
            v-else
            src="@/assets/icons/common/close.svg"
            alt="close"
            class="close-icon"
            loading="lazy"
          />
        </div>
      </div>
    </n-layout-header>

    <!-- 使用封装的自定义抽屉组件 -->
    <custom-drawer
      v-model:show="showDrawer"
      :header-height="headerHeight"
      @close="showDrawer = false"
    >
      <!-- 菜单列表 -->
      <div class="mobile-menu mt-[1rem] px-[0.84rem] overflow-y-auto">
        <div class="w-full flex flex-col gap-[0.68rem] pb-[2rem]">
          <!-- 一级菜单项 -->
          <div v-for="(item, index) in navData" :key="index" class="menu-item">
            <!-- 一级菜单标题 -->
            <div class="menu-header">
              <span
                class="menu-title"
                @click="item.path && navigateTo(item.path)"
                :class="{
                  'cursor-pointer hover:text-[#E50113]': item.path,
                  'hover:text-[#7F7F7F]': !item.path,
                  'text-[#7f7f7f]':
                    expandedItems.includes(item.key) && !item.path,
                }"
                >{{ item.label }}</span
              >
              <span
                v-if="item.children && item.children.length > 0"
                class="expand-icon cursor-pointer"
                @click="toggleMenu(item.key)"
              >
                <img
                  :src="
                    expandedItems.includes(item.key) ? arrowTopRed : arrowDown
                  "
                  :alt="
                    expandedItems.includes(item.key) ? 'collapse' : 'expand'
                  "
                  class="icon-size"
                  loading="lazy"
                />
              </span>
            </div>

            <!-- 二级菜单 -->
            <transition name="menu-expand">
              <div
                v-if="item.children && item.children.length > 0"
                class="submenu-container mt-[0.48rem] flex flex-col gap-[0.36rem]"
                v-show="expandedItems.includes(item.key)"
              >
                <!-- 二级菜单项 -->
                <div
                  v-for="(subItem, subIndex) in item.children"
                  :key="subIndex"
                  class="submenu-item"
                >
                  <!-- 二级菜单标题 -->
                  <div class="submenu-header">
                    <div class="flex items-center w-full">
                      <span
                        class="submenu-title max-w-[5.4rem]"
                        @click="subItem.path && navigateTo(subItem.path)"
                        :class="{
                          'cursor-pointer hover:text-[#E50113]': subItem.path,
                          'hover:text-[#7F7F7F]': !subItem.path,
                          'text-[#7f7f7f]':
                            expandedSubItems.includes(subItem.key) &&
                            !subItem.path,
                        }"
                        >{{ subItem.label }}</span
                      >
                      <img
                        v-if="subItem.icon"
                        :src="subItem.icon"
                        alt="icon"
                        class="h-[0.32rem] ml-[0.24rem]"
                        loading="lazy"
                      />
                    </div>

                    <span
                      v-if="subItem.children && subItem.children.length > 0"
                      class="expand-icon cursor-pointer"
                      @click="toggleSubMenu(subItem.key)"
                    >
                      <img
                        :src="
                          expandedSubItems.includes(subItem.key)
                            ? collapseRed
                            : expand
                        "
                        :alt="
                          expandedSubItems.includes(subItem.key)
                            ? 'collapse'
                            : 'expand'
                        "
                        class="submenu-icon-size"
                        loading="lazy"
                      />
                    </span>
                  </div>

                  <!-- 三级菜单 -->
                  <transition name="submenu-expand">
                    <div
                      v-if="subItem.children && subItem.children.length > 0"
                      class="third-menu-container flex flex-col gap-[0.24rem] mt-[0.24rem]"
                      v-show="expandedSubItems.includes(subItem.key)"
                    >
                      <div
                        v-for="(thirdItem, thirdIndex) in subItem.children"
                        :key="thirdIndex"
                        class="third-menu-item"
                        @click="thirdItem.path && navigateTo(thirdItem.path)"
                        :class="{
                          'cursor-pointer hover:text-[#E50113]': thirdItem.path,
                          'hover:text-[#7F7F7F]': !thirdItem.path,
                          'text-[#7f7f7f]':
                            expandedSubItems.includes(thirdItem.key) &&
                            !thirdItem.path,
                        }"
                      >
                        {{ thirdItem.label }}
                      </div>
                    </div>
                  </transition>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
    </custom-drawer>
  </div>
</template>

<script lang="ts" setup>
import moreMenu from "@/assets/icons/common/more-menu.svg";
import expand from "@/assets/icons/common/expand.svg";
import collapseRed from "@/assets/icons/common/collapse-red.svg";
import arrowDown from "@/assets/icons/common/arrow-down.svg";
import arrowTopRed from "@/assets/icons/common/arrow-top-red.svg";
import hotIcon from "@/assets/icons/common/hot-icon.svg";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const showDrawer = ref(false);
const headerHeight = ref("1.6rem");

// 记录展开的菜单项
const expandedItems = ref<string[]>([]);
const expandedSubItems = ref<string[]>([]);

// 设置CSS变量
onMounted(() => {
  document.documentElement.style.setProperty(
    "--header-height",
    headerHeight.value
  );
});

// 切换抽屉状态
function toggleDrawer(): void {
  showDrawer.value = !showDrawer.value;
}

// 切换菜单展开/折叠状态
function toggleMenu(key: string): void {
  const index = expandedItems.value.indexOf(key);
  if (index === -1) {
    expandedItems.value.push(key);
  } else {
    expandedItems.value.splice(index, 1);
  }
}

// 切换子菜单展开/折叠状态
function toggleSubMenu(key: string): void {
  const index = expandedSubItems.value.indexOf(key);
  if (index === -1) {
    expandedSubItems.value.push(key);
  } else {
    expandedSubItems.value.splice(index, 1);
  }
}

// 菜单项接口定义
interface MenuItem {
  label: string;
  key: string;
  path?: string;
  children?: MenuItem[];
  icon?: string;
}

// 导航数据
const navData: MenuItem[] = [
  {
    label: "NOSOTROS",
    key: "NOSOTROS",
    children: [
      {
        label: "Quiénes somos",
        key: "Quiénes somos",
        path: "/h5/quienes-somos",
      },
      {
        label: "Cómo trabajarnos",
        key: "Cómo trabajarnos",
        path: "/h5/como-trabajarnos",
      },
    ],
  },
  {
    label: "SERVICIOS",
    key: "SERVICIOS",
    path: "/h5/servicios",
    children: [
      {
        label: "Asesoría de compras",
        key: "Asesoría de compras",
        path: "/h5/servicios#asesoria-de-compras",
      },
      {
        label: "Búsqueda de proveedores y productos",
        key: "Búsqueda de proveedores y productos",
        path: "/h5/busqueda-de-proveedores-y-productos",
      },
      {
        label: "Validación de fabricantes",
        key: "Validación de fabricantes",
        path: "/h5/servicios#validacion-de-fabricantes",
      },
      {
        label: "Viajes de negocios a China",
        key: "Viajes de negocios a China",
        path: "/h5/servicios#viajes-de-negocios-a-china",
      },
      {
        label: "Agente de compras",
        key: "Agente de compras",
        path: "/h5/viajar-a-china",
      },
      {
        label: "Control de calidad",
        key: "Control de calidad",
        path: "/h5/servicios#control-de-calidad",
      },
      {
        label: "Consolidación de cargas y logística",
        key: "Consolidación de cargas y logística",
        path: "/h5/consolidacion-de-cargas-y-logistica",
      },
      {
        label: "Fotos del producto y catálogo",
        key: "Fotos del producto y catálogo",
        path: "/h5/servicios#fotos-del-producto-y-catalogo",
      },
    ],
  },
  {
    label: "IMPORTACIÓN",
    key: "IMPORTACIÓN",
    children: [
      {
        label: "Viajar a china",
        key: "Viajar a china",
        path: "/h5/viajar-a-china",
      },
      {
        label: "Búsqueda de proveedores y productos",
        key: "Búsqueda de proveedores y productos",
        path: "/h5/busqueda-de-proveedores-y-productos",
      },
      {
        icon: hotIcon,
        label: "Selección online",
        key: "Selección online",
        path: "https://shop.chilat.com/h5/",
      },
      {
        label: "Garantía de importación",
        key: "Garantía de importación",
        path: "/h5/servicio-de-garantia-de-importacion",
      },
    ],
  },
  {
    label: "FERIAS Y MERCADO",
    key: "FERIAS Y MERCADO",
    children: [
      {
        label: "Cantón",
        key: "Cantón",
        path: "/h5/canton",
        children: [
          {
            label: "Feria de Cantón",
            key: "Feria de Cantón",
            path: "/h5/feria-de-canton",
          },
          {
            label: "Mercado mayorista en Guangzhou",
            key: "Mercado mayorista en Guangzhou",
            path: "/h5/mercado-de-guangzhou",
          },
          {
            label: "Mercado circundante de Guangzhou",
            key: "Mercado circundante de Guangzhou",
            path: "/h5/mercado-circundante-de-guangzhou",
          },
        ],
      },
      {
        label: "Yiwu",
        key: "Yiwu",
        path: "/h5/yiwu",
        children: [
          // {
          //   label: "Las ventajas únicas del mercado de Yiwu",
          //   key: "Las ventajas únicas del mercado de Yiwu",
          //   path: "/h5/las-ventajas-unicas-del-mercado-de-yiwu",
          // },
          {
            label: "Viaja al mercado de yiwu",
            key: "Viaja al mercado de yiwu",
            path: "/h5/viaja-al-mercado-de-yiwu",
          },
          {
            label: "Preguntas frecuentes sobre Yiwu",
            key: "Preguntas frecuentes sobre Yiwu",
            path: "/h5/preguntas-frecuentes-sobre-yiwu",
          },
          {
            label: "Alojamiento y comida en Yiwu",
            key: "Alojamiento y comida en Yiwu",
            path: "/h5/alojamiento-y-comida-en-yiwu",
          },
          {
            label: "Guía del mercado de Yiwu",
            key: "Guía del mercado de Yiwu",
            path: "/h5/guia-del-mercado-de-yiwu",
          },
          {
            label: "Yiwu Fair",
            key: "Yiwu Fair",
            path: "/h5/yiwu-fair",
          },
          {
            label: "Feria de Yiwu",
            key: "Feria de Yiwu",
            path: "/h5/feria-de-yiwu",
          },
        ],
      },
    ],
  },
  {
    label: "ELÍJANOS",
    key: "ELÍJANOS",
    path: "/h5/elijanos",
    children: [
      {
        label: "Productos y calidad",
        key: "productos-y-calidad",
        path: "/h5/elijanos#productos-y-calidad",
      },
      {
        label: "Consolidaciones y contenedor completo",
        key: "consolidaciones-y-contenedor-completo",
        path: "/h5/elijanos#consolidaciones-y-contenedor-completo",
      },
      {
        label: "Acompañamiento y traducción",
        key: "acompanamiento-y-traduccion",
        path: "/h5/elijanos#acompanamiento-y-traduccion",
      },
    ],
  },
  {
    label: "SOCIO DE NEGOCIOS",
    key: "socio-de-negocios",
    path: "/h5/socio-de-negocios",
  },
  {
    label: "CONTACTO",
    key: "contacto",
    path: "/h5/contacto",
  },
  {
    label: "BLOG",
    key: "blog",
    path: "/h5/blog",
  },
];

// 导航跳转
function navigateTo(path: string): void {
  if (path.includes("/h5/contacto")) {
    //特殊处理
    onWhatsAppClick();
  } else if (path.includes("http")) {
    window.open(path, "_blank");
  } else {
    router.push(path);
  }
  showDrawer.value = false;
}
</script>

<style scoped>
/* 头部样式 */
.mobile-header {
  background-color: #fff;
  position: relative;
  width: 100%;
  z-index: 1001;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.64rem 0.8rem 0.4rem 0.4rem;
}

.logo {
  width: 2.36rem;
  height: auto;
}

.close-icon {
  width: 0.4rem;
  height: 0.4rem;
}

/* 一级菜单 */
.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 0.4rem;
  line-height: 0.4rem;
}

.icon-size {
  width: 0.3rem;
  height: 0.3rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 二级菜单 */
.submenu-container {
  padding-left: 0.32rem;
}

.submenu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  color: #333;
  font-size: 0.32rem;
  line-height: 0.4rem;
}

.submenu-icon-size {
  width: 0.28rem;
  height: 0.28rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 三级菜单 */
.third-menu-item {
  color: #4d4d4d;
  cursor: pointer;
  font-size: 0.28rem;
  line-height: 0.4rem;
  padding-left: 0.28rem;
}

/* 菜单展开动画 */
.menu-expand-enter-active,
.menu-expand-leave-active,
.submenu-expand-enter-active,
.submenu-expand-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.menu-expand-enter-from,
.menu-expand-leave-to,
.submenu-expand-enter-from,
.submenu-expand-leave-to {
  opacity: 0;
  max-height: 0;
}

.menu-expand-enter-to,
.menu-expand-leave-from {
  opacity: 1;
  max-height: 20rem;
}

.submenu-expand-enter-to,
.submenu-expand-leave-from {
  opacity: 1;
  max-height: 10rem;
}

/* 移动导航容器 */
.mobile-nav-container {
  position: relative;
  width: 100%;
}

.mobile-menu {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
}
</style>
