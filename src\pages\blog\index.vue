<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div v-if="!!queryForm.publishDate">
      <!-- 面包屑导航 -->
      <div class="mt-[50px]">
        <n-breadcrumb separator=">">
          <n-breadcrumb-item>
            <icon-card
              size="24"
              color="#7F7F7F"
              class="mr-[-4px] cursor-pointer"
              name="ic:sharp-home"
              @click="onNavigateHome"
            >
            </icon-card>
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            <div class="text-[#7F7F7F] text-[14px] font-400 leading-[14px]">
              <a href="/blog"> Blog </a>
            </div>
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            <div class="text-[#333] text-[14px] font-500 leading-[14px]">
              <span>Etpublicado en </span>
              <span>{{ timeFormatByZone(Number(route.query.date), false, false, false) ?? "Por <PERSON>" }}</span>
            </div>
          </n-breadcrumb-item>
        </n-breadcrumb>
      </div>
    </div>
    <div v-else>
      <div class="page-header text-[#fff] overflow-auto py-[50px] pl-[24px]">
        <Breadcrumb :items="breadcrumbItems" />
        <div class="text-[80px] leading-[80px] font-medium mt-[40px]">Blog</div>
      </div>
      <div class="mx-[38px] px-[24px]">
        <!-- 标签列表 -->
        <div class="category-container mt-[56px]">
          <div class="category-items" :class="{ expanded: pageData.isExpanded }">
            <span class="category-items-inner">
              <span v-for="(category, index) in pageData.categoryList" :key="index" class="category-item">
                <n-button
                  round
                  :bordered="false"
                  :type="category?.isSelected ? 'primary' : 'tertiary'"
                  @click="() => onCategoryClick(category)"
                  class="text-[14px] leading-[14px] px-[12px] py-[6px] h-[26px]"
                  :class="[category?.isSelected ? 'font-500 bg-[#E50113]' : 'font-400 bg-[#F2F2F2]']"
                >
                  {{ category.name }}
                </n-button></span
              >
            </span>
          </div>
          <icon-card
            size="24"
            color="black"
            class="px-[6px]"
            :name="pageData.isExpanded ? 'iconoir:nav-arrow-down' : 'iconoir:nav-arrow-up'"
            @click="onToggleExpand"
          ></icon-card>
        </div>
        <!-- 搜索 -->
        <div
          v-if="pageData.articleList?.length > 0"
          class="flex justify-between mt-[48px] py-[18px] border-b border-[#7F7F7F]"
        >
          <div class="text-[34px] font-medium leading-[34px]">Todos los artículos</div>
          <div class="text-white">
            <n-input
              round
              :style="{ width: '20vw' }"
              v-model:value="queryForm.keyword"
              :on-blur="onSearchKeyword"
              @keydown.enter="onSearchKeyword"
            >
              <template #suffix>
                <icon-card
                  size="32"
                  name="mynaui:search"
                  class="add-btn-list"
                  color="#E50113"
                  @click="onSearchKeyword"
                ></icon-card>
              </template>
            </n-input>
          </div>
        </div>
      </div>
    </div>

    <!-- 文章列表 -->
    <div class="mb-[158px] mx-[38px] px-[24px]" v-if="pageData.articleList?.length > 0">
      <div v-for="(article, index) in pageData.articleList" :key="index">
        <div class="flex py-[30px] border-b border-[#F2F2F2] article-item">
          <div @click="() => onArticleDetail(article)">
            <div v-if="article?.logo">
              <img :src="article?.logo" class="w-[464px] h-[232px] rounded-[20px]" loading="lazy"  alt="blog">
            </div>
            <div v-else>
              <n-skeleton height="232px" width="464px" class="rounded-[20px]" />
            </div>
          </div>
          <div class="w-[640px] ml-[52px]">
            <div class="flex flex-col gap-4">
              <div class="flex flex-wrap gap-1">
                <div v-for="category in article.articleCategories" :key="category.id">
                  <n-button
                    round
                    :bordered="false"
                    :type="category?.isSelected ? 'primary' : 'tertiary'"
                    @click="onCategoryClick(category)"
                    class="text-[14px] leading-[14px] px-[12px] py-[6px] h-[26px]"
                    :class="[category?.isSelected ? 'font-500 bg-[#E50113]' : 'font-400 bg-[#F2F2F2]']"
                  >
                    {{ category.name }}
                  </n-button>
                </div>
              </div>
              <div
                @click="() => onArticleDetail(article)"
                class="w-full mt-[14px] text-[24px] text-[#333] font-500 leading-[28px]"
              >
                <p class="line-clamp-2">
                  {{ article.title }}
                </p>
              </div>
              <div
                @click="() => onArticleDetail(article)"
                class="w-full mt-[14px] text-[16px] font-400 leading-[24px] text-[#7F7F7F]"
              >
                <p class="line-clamp-2">
                  {{ article.introduce }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-[90px]">
        <n-pagination
          show-size-picker
          show-quick-jumper
          :page-sizes="[20, 50, 100]"
          :page-count="pageData.pageCount"
          v-model:page="queryForm.pageNum"
          v-model:page-size="queryForm.pageSize"
          :on-update:page="onUpdatePageNum"
          :on-update:page-size="onUpdatePageSize"
          class="mt-3 text-center flex justify-center"
        >
          <template #prefix>
            <span class="px-2">Total:</span>
            <span>{{ pageData.pageItems }}</span>
          </template>
          <template #goto>Ir a</template>
        </n-pagination>
      </div>
    </div>
    <!-- 没有文章 -->
    <div class="mx-[38px] px-[24px] mt-[50px]" v-else>
      <div class="flex flex-col gap-2">
        <div class="flex justify-start text-[#333] text-[34px] font-500 leading-[34px] my-[20px]">
          No se encontró nada
        </div>
        <hr class="border-t border-[#7F7F7F]" />
        <div class="flex justify-between mt-[30px] mb-[400px]">
          <div class="text-[#666] text-[18px] font-400 leading-[27px]">
            <div>Parece que no podemos encontrar lo que estas buscando.</div>
            <div>Tal vez, continuar buscando pueda ayudar.</div>
          </div>
          <div class="text-white">
            <n-input-group>
              <n-input
                round
                status="error"
                :style="{
                  width: '20vw',
                }"
                v-model:value="queryForm.keyword"
                :on-blur="onSearchKeyword"
                @keydown.enter="onSearchKeyword"
              />
              <n-button round color="#E50113" @click="onSearchKeyword">
                <icon-card size="24" name="mynaui:search" color="#fff"></icon-card>
                <span class="ml-1 text[18px]">Buscar</span>
              </n-button>
            </n-input-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import homeIcon from "@/assets/icons/common/home.svg";
import { reactive } from "vue";

useHead({
  title: "Blog de abastecimiento mayorista en China-chilat News - Chilat",
  meta: [
    {
      name: "description",
      content:
        "Proporcionamos conocimientos sobre la compra al por mayor de productos en China y resolvemos consultas relacionadas con la importación, el transporte y los precios.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/blog/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/blog/`,
    },
  ],
});

const route = useRoute();

const pageData = reactive<any>({
  pageCount: 1,
  pageItems: 1,
  isExpanded: false,
  cateIndex: 0,
  articleList: null, // 文章列表
  categoryList: [{ id: undefined, name: "Todos los artículos", isSelected: true }],
});

const queryForm = reactive<any>({
  keyword: null,
  publishDate: null,
  cateId: null,
  pageSize: 20,
  pageNum: 1,
});

const breadcrumbItems: any = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/blog", text: "Blog" },
];

onMounted(() => {
  queryForm.cateId = route.query.cateId ?? undefined;
  queryForm.publishDate = route.query.date ?? undefined;
  // 获取文章分类列表
  onListArticleCategory();
  // 获取文章列表
  onPageList();
});

onBeforeUnmount(() => {
  Object.keys(queryForm).forEach((key) => {
    queryForm[key] = null;
  });
  Object.keys(pageData).forEach((key) => {
    pageData[key] = null;
  });
});

async function onPageList() {
  const publishDate = timeFormatByZone(Number(queryForm.publishDate), false, false, false)?.trim();

  const res: any = await useSearchArticleList({
    page: {
      current: queryForm.pageNum,
      size: queryForm.pageSize,
    },
    keyword: queryForm.keyword,
    cateId: queryForm.cateId ?? null,
    publishDate: publishDate ?? null,
  });
  if (res?.result?.code === 200) {
    pageData.articleList = res.data;
    pageData.pageCount = res.page?.pages;
    pageData.pageItems = res.page.total;
    pageData.articleList = pageData.articleList?.map((articleItem: any) => {
      articleItem.articleCategories = articleItem.articleCategories?.map((categoryItem: any) => {
        if (categoryItem.id === queryForm.cateId) {
          categoryItem["isSelected"] = true;
        } else {
          categoryItem["isSelected"] = false;
        }
        return categoryItem;
      });
      return articleItem;
    });
  }
}

// 获取文章分类列表
async function onListArticleCategory() {
  const res: any = await useListArticleCategory({
    names: ["Chilat"],
  });
  if (res?.result?.code === 200) {
    for (let i = 0; i < res.data.length; i++) {
      const item = res.data[i];
      for (let j = 0; j < item?.children.length; j++) {
        const child = item.children[j];
        child["isSelected"] = route.query.cateId === child.id ? true : false;
        pageData.categoryList.push(child);
      }
    }
  }
}

// 搜索关键字
async function onSearchKeyword() {
  queryForm.pageNum = 1;
  queryForm.keyword = queryForm.keyword?.trim();
  await onPageList();
}

function onToggleExpand() {
  pageData.isExpanded = !pageData.isExpanded;
}

async function onCategoryClick(category: any) {
  queryForm.pageNum = 1;
  queryForm.cateId = category.id ?? null;
  pageData.categoryList = pageData.categoryList?.map((item: any) => {
    if (item.id === category.id) {
      item["isSelected"] = true;
    } else {
      item["isSelected"] = false;
    }
    return item;
  });

  await onPageList();
}

async function onUpdatePageNum(page: number) {
  queryForm.pageNum = page;
  await onPageList();
}

async function onUpdatePageSize(pageSize: number) {
  queryForm.pageNum = 1;
  queryForm.pageSize = pageSize;
  await onPageList();
}

function onArticleDetail(article: any) {
  return navigateTo({
    path: `/blog/${article.id}`,
    // 可以传递状态实现共享元素过渡
    state: { transitionName: "page-slide" },
  });
}

function onNavigateHome() {
  navigateToPage("/", {}, false);
}
</script>

<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%;
  background-image: url("@/assets/icons/blog/header-bg.png");
  background-repeat: no-repeat;
}

.category-container {
  display: flex;
  align-items: flex-start;
  max-width: 1156px;
  border-radius: 4px;
}

.category-container::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 50px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
  pointer-events: none;
}

.category-items {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  // text-overflow: ellipsis;
  line-height: 14px;
  justify-content: center;
}

.category-items.expanded {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
}

.category-items-inner {
  display: inline;
}

.category-item {
  display: inline-block;
  margin-right: 6px;
  margin-bottom: 6px;
}

.article-item {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.article-item:hover {
  transform: scale(1.05);
}
</style>
