/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import Long from "long";

export const protobufPackage = "common";

export interface EmptyParam {
}

export interface IdParam {
  id: string;
}

export interface IdsParam {
  ids: string[];
}

export interface CodeParam {
  code: string;
}

export interface CodesParam {
  codes: string[];
}

export interface IdValueParam {
  id: string;
  value: string;
}

export interface IdValuesParam {
  id: string;
  values: string[];
}

export interface StringParam {
  str: string;
}

export interface StringsParam {
  strs: string[];
}

export interface IntParam {
  value: number;
}

export interface LongParam {
  value: number;
}

export interface DoubleParam {
  value: number;
}

export interface EnabledParam {
  id: string;
  enabled: boolean;
}

export interface BatchEnabledParam {
  ids: string[];
  enabled: boolean;
}

export interface PageParam {
  /** 当前页 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 排序 */
  sort: SortParam[];
}

export interface MapParam {
  map: { [key: string]: string };
}

export interface MapParam_MapEntry {
  key: string;
  value: string;
}

export interface Result {
  code: number;
  message: string;
}

export interface ApiResult {
  result: Result | undefined;
}

export interface UploadResult {
  result: Result | undefined;
  data: string;
}

export interface DownloadResult {
  result: Result | undefined;
  data: File | undefined;
}

export interface IntResult {
  result: Result | undefined;
  data: number;
}

export interface LongResult {
  result: Result | undefined;
  data: number;
}

export interface IntsResult {
  result: Result | undefined;
  data: number[];
}

export interface DoubleResult {
  result: Result | undefined;
  data: number;
}

export interface IdResult {
  result: Result | undefined;
  data: string;
}

export interface IdsResult {
  result: Result | undefined;
  data: string[];
}

export interface StringResult {
  result: Result | undefined;
  data: string;
}

export interface StringsResult {
  result: Result | undefined;
  data: string[];
}

export interface BooleanResult {
  result: Result | undefined;
  data: boolean;
}

export interface BooleansResult {
  result: Result | undefined;
  data: boolean[];
}

export interface IdNameResult {
  result: Result | undefined;
  data: IdNameModel | undefined;
}

export interface NameValueResult {
  result: Result | undefined;
  data: NameValueModel | undefined;
}

export interface NameValuesResult {
  result: Result | undefined;
  data: NameValuesModel | undefined;
}

export interface MapResult {
  result: Result | undefined;
  data: { [key: string]: string };
}

export interface MapResult_DataEntry {
  key: string;
  value: string;
}

export interface LogResult {
  result: Result | undefined;
  data: LogModel[];
}

export interface ImportResult {
  result: Result | undefined;
  data: ImportModel | undefined;
}

export interface SortParam {
  /** 排序字段 */
  orderBy: string;
  /** 是否升序 */
  isAsc: boolean;
}

export interface IdNameModel {
  id: string;
  name: string;
}

export interface CodeNameModel {
  code: string;
  name: string;
}

export interface NameValueModel {
  name: string;
  value: string;
}

export interface NameCodeModel {
  name: string;
  code: number;
}

export interface NameValuesModel {
  name: string;
  values: string[];
}

export interface LogModel {
  /** 日志ID */
  id: number;
  /** 操作 */
  action: string;
  /** 日志说明 */
  remark: string;
  /** 日志图片 */
  images: string[];
  /** 操作人 */
  createBy: string;
  /** 操作时间 */
  createTime: number;
}

export interface ImportModel {
  /** 导入总数 */
  totalCount: number;
  /** 成功总数 */
  successCount: number;
  /** 失败总数 */
  failCount: number;
  /** 失败文件地址 */
  downloadUrl: string;
}

export interface Page {
  /** 当前页 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 总页数 */
  pages: number;
  /** 总数 */
  total: number;
}

export interface FileInfo {
  fileName: string;
  fileUrl: string;
}

export interface File {
  header: FileHeader | undefined;
  data: Uint8Array;
}

export interface FileHeader {
  filename: string;
  contentType: string;
  mime: { [key: string]: Strings };
}

export interface FileHeader_MimeEntry {
  key: string;
  value: Strings | undefined;
}

export interface Strings {
  value: string[];
}

export interface WebapiOption {
  /** 使用multipart上传文件 */
  upload: boolean;
  /** 使用二进制流而不是json */
  download: boolean;
}

export interface MessageOption {
  /** 是否生成对象 */
  generated: boolean;
}

function createBaseEmptyParam(): EmptyParam {
  return {};
}

export const EmptyParam = {
  encode(_: EmptyParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EmptyParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmptyParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): EmptyParam {
    return {};
  },

  toJSON(_: EmptyParam): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<EmptyParam>, I>>(base?: I): EmptyParam {
    return EmptyParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmptyParam>, I>>(_: I): EmptyParam {
    const message = createBaseEmptyParam();
    return message;
  },
};

function createBaseIdParam(): IdParam {
  return { id: "" };
}

export const IdParam = {
  encode(message: IdParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdParam {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: IdParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdParam>, I>>(base?: I): IdParam {
    return IdParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdParam>, I>>(object: I): IdParam {
    const message = createBaseIdParam();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseIdsParam(): IdsParam {
  return { ids: [] };
}

export const IdsParam = {
  encode(message: IdsParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.ids) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdsParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdsParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ids.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdsParam {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.String(e)) : [] };
  },

  toJSON(message: IdsParam): unknown {
    const obj: any = {};
    if (message.ids?.length) {
      obj.ids = message.ids;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdsParam>, I>>(base?: I): IdsParam {
    return IdsParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdsParam>, I>>(object: I): IdsParam {
    const message = createBaseIdsParam();
    message.ids = object.ids?.map((e) => e) || [];
    return message;
  },
};

function createBaseCodeParam(): CodeParam {
  return { code: "" };
}

export const CodeParam = {
  encode(message: CodeParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CodeParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCodeParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CodeParam {
    return { code: isSet(object.code) ? globalThis.String(object.code) : "" };
  },

  toJSON(message: CodeParam): unknown {
    const obj: any = {};
    if (message.code !== "") {
      obj.code = message.code;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CodeParam>, I>>(base?: I): CodeParam {
    return CodeParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CodeParam>, I>>(object: I): CodeParam {
    const message = createBaseCodeParam();
    message.code = object.code ?? "";
    return message;
  },
};

function createBaseCodesParam(): CodesParam {
  return { codes: [] };
}

export const CodesParam = {
  encode(message: CodesParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.codes) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CodesParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCodesParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.codes.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CodesParam {
    return { codes: globalThis.Array.isArray(object?.codes) ? object.codes.map((e: any) => globalThis.String(e)) : [] };
  },

  toJSON(message: CodesParam): unknown {
    const obj: any = {};
    if (message.codes?.length) {
      obj.codes = message.codes;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CodesParam>, I>>(base?: I): CodesParam {
    return CodesParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CodesParam>, I>>(object: I): CodesParam {
    const message = createBaseCodesParam();
    message.codes = object.codes?.map((e) => e) || [];
    return message;
  },
};

function createBaseIdValueParam(): IdValueParam {
  return { id: "", value: "" };
}

export const IdValueParam = {
  encode(message: IdValueParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdValueParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdValueParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdValueParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: IdValueParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdValueParam>, I>>(base?: I): IdValueParam {
    return IdValueParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdValueParam>, I>>(object: I): IdValueParam {
    const message = createBaseIdValueParam();
    message.id = object.id ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseIdValuesParam(): IdValuesParam {
  return { id: "", values: [] };
}

export const IdValuesParam = {
  encode(message: IdValuesParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    for (const v of message.values) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdValuesParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdValuesParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdValuesParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: IdValuesParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.values?.length) {
      obj.values = message.values;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdValuesParam>, I>>(base?: I): IdValuesParam {
    return IdValuesParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdValuesParam>, I>>(object: I): IdValuesParam {
    const message = createBaseIdValuesParam();
    message.id = object.id ?? "";
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseStringParam(): StringParam {
  return { str: "" };
}

export const StringParam = {
  encode(message: StringParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.str !== "") {
      writer.uint32(10).string(message.str);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.str = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringParam {
    return { str: isSet(object.str) ? globalThis.String(object.str) : "" };
  },

  toJSON(message: StringParam): unknown {
    const obj: any = {};
    if (message.str !== "") {
      obj.str = message.str;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StringParam>, I>>(base?: I): StringParam {
    return StringParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringParam>, I>>(object: I): StringParam {
    const message = createBaseStringParam();
    message.str = object.str ?? "";
    return message;
  },
};

function createBaseStringsParam(): StringsParam {
  return { strs: [] };
}

export const StringsParam = {
  encode(message: StringsParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.strs) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringsParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringsParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.strs.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringsParam {
    return { strs: globalThis.Array.isArray(object?.strs) ? object.strs.map((e: any) => globalThis.String(e)) : [] };
  },

  toJSON(message: StringsParam): unknown {
    const obj: any = {};
    if (message.strs?.length) {
      obj.strs = message.strs;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StringsParam>, I>>(base?: I): StringsParam {
    return StringsParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringsParam>, I>>(object: I): StringsParam {
    const message = createBaseStringsParam();
    message.strs = object.strs?.map((e) => e) || [];
    return message;
  },
};

function createBaseIntParam(): IntParam {
  return { value: 0 };
}

export const IntParam = {
  encode(message: IntParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== 0) {
      writer.uint32(8).int32(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.value = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntParam {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: IntParam): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IntParam>, I>>(base?: I): IntParam {
    return IntParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntParam>, I>>(object: I): IntParam {
    const message = createBaseIntParam();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLongParam(): LongParam {
  return { value: 0 };
}

export const LongParam = {
  encode(message: LongParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== 0) {
      writer.uint32(8).int64(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongParam {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: LongParam): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LongParam>, I>>(base?: I): LongParam {
    return LongParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongParam>, I>>(object: I): LongParam {
    const message = createBaseLongParam();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseDoubleParam(): DoubleParam {
  return { value: 0 };
}

export const DoubleParam = {
  encode(message: DoubleParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== 0) {
      writer.uint32(9).double(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DoubleParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDoubleParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.value = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DoubleParam {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: DoubleParam): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DoubleParam>, I>>(base?: I): DoubleParam {
    return DoubleParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoubleParam>, I>>(object: I): DoubleParam {
    const message = createBaseDoubleParam();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseEnabledParam(): EnabledParam {
  return { id: "", enabled: false };
}

export const EnabledParam = {
  encode(message: EnabledParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.enabled !== false) {
      writer.uint32(16).bool(message.enabled);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EnabledParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEnabledParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.enabled = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EnabledParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      enabled: isSet(object.enabled) ? globalThis.Boolean(object.enabled) : false,
    };
  },

  toJSON(message: EnabledParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.enabled !== false) {
      obj.enabled = message.enabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EnabledParam>, I>>(base?: I): EnabledParam {
    return EnabledParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnabledParam>, I>>(object: I): EnabledParam {
    const message = createBaseEnabledParam();
    message.id = object.id ?? "";
    message.enabled = object.enabled ?? false;
    return message;
  },
};

function createBaseBatchEnabledParam(): BatchEnabledParam {
  return { ids: [], enabled: false };
}

export const BatchEnabledParam = {
  encode(message: BatchEnabledParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.ids) {
      writer.uint32(10).string(v!);
    }
    if (message.enabled !== false) {
      writer.uint32(16).bool(message.enabled);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BatchEnabledParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchEnabledParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ids.push(reader.string());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.enabled = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BatchEnabledParam {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.String(e)) : [],
      enabled: isSet(object.enabled) ? globalThis.Boolean(object.enabled) : false,
    };
  },

  toJSON(message: BatchEnabledParam): unknown {
    const obj: any = {};
    if (message.ids?.length) {
      obj.ids = message.ids;
    }
    if (message.enabled !== false) {
      obj.enabled = message.enabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BatchEnabledParam>, I>>(base?: I): BatchEnabledParam {
    return BatchEnabledParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchEnabledParam>, I>>(object: I): BatchEnabledParam {
    const message = createBaseBatchEnabledParam();
    message.ids = object.ids?.map((e) => e) || [];
    message.enabled = object.enabled ?? false;
    return message;
  },
};

function createBasePageParam(): PageParam {
  return { current: 0, size: 0, sort: [] };
}

export const PageParam = {
  encode(message: PageParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.current !== 0) {
      writer.uint32(8).int64(message.current);
    }
    if (message.size !== 0) {
      writer.uint32(16).int64(message.size);
    }
    for (const v of message.sort) {
      SortParam.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PageParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.current = longToNumber(reader.int64() as Long);
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.size = longToNumber(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.sort.push(SortParam.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageParam {
    return {
      current: isSet(object.current) ? globalThis.Number(object.current) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      sort: globalThis.Array.isArray(object?.sort) ? object.sort.map((e: any) => SortParam.fromJSON(e)) : [],
    };
  },

  toJSON(message: PageParam): unknown {
    const obj: any = {};
    if (message.current !== 0) {
      obj.current = Math.round(message.current);
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    if (message.sort?.length) {
      obj.sort = message.sort.map((e) => SortParam.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageParam>, I>>(base?: I): PageParam {
    return PageParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageParam>, I>>(object: I): PageParam {
    const message = createBasePageParam();
    message.current = object.current ?? 0;
    message.size = object.size ?? 0;
    message.sort = object.sort?.map((e) => SortParam.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMapParam(): MapParam {
  return { map: {} };
}

export const MapParam = {
  encode(message: MapParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    Object.entries(message.map).forEach(([key, value]) => {
      MapParam_MapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MapParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMapParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = MapParam_MapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.map[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MapParam {
    return {
      map: isObject(object.map)
        ? Object.entries(object.map).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: MapParam): unknown {
    const obj: any = {};
    if (message.map) {
      const entries = Object.entries(message.map);
      if (entries.length > 0) {
        obj.map = {};
        entries.forEach(([k, v]) => {
          obj.map[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MapParam>, I>>(base?: I): MapParam {
    return MapParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MapParam>, I>>(object: I): MapParam {
    const message = createBaseMapParam();
    message.map = Object.entries(object.map ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseMapParam_MapEntry(): MapParam_MapEntry {
  return { key: "", value: "" };
}

export const MapParam_MapEntry = {
  encode(message: MapParam_MapEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MapParam_MapEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMapParam_MapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MapParam_MapEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: MapParam_MapEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MapParam_MapEntry>, I>>(base?: I): MapParam_MapEntry {
    return MapParam_MapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MapParam_MapEntry>, I>>(object: I): MapParam_MapEntry {
    const message = createBaseMapParam_MapEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseResult(): Result {
  return { code: 0, message: "" };
}

export const Result = {
  encode(message: Result, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Result {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Result {
    return {
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: Result): unknown {
    const obj: any = {};
    if (message.code !== 0) {
      obj.code = Math.round(message.code);
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Result>, I>>(base?: I): Result {
    return Result.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Result>, I>>(object: I): Result {
    const message = createBaseResult();
    message.code = object.code ?? 0;
    message.message = object.message ?? "";
    return message;
  },
};

function createBaseApiResult(): ApiResult {
  return { result: undefined };
}

export const ApiResult = {
  encode(message: ApiResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ApiResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApiResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ApiResult {
    return { result: isSet(object.result) ? Result.fromJSON(object.result) : undefined };
  },

  toJSON(message: ApiResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ApiResult>, I>>(base?: I): ApiResult {
    return ApiResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApiResult>, I>>(object: I): ApiResult {
    const message = createBaseApiResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    return message;
  },
};

function createBaseUploadResult(): UploadResult {
  return { result: undefined, data: "" };
}

export const UploadResult = {
  encode(message: UploadResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== "") {
      writer.uint32(18).string(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UploadResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.String(object.data) : "",
    };
  },

  toJSON(message: UploadResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== "") {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UploadResult>, I>>(base?: I): UploadResult {
    return UploadResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UploadResult>, I>>(object: I): UploadResult {
    const message = createBaseUploadResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? "";
    return message;
  },
};

function createBaseDownloadResult(): DownloadResult {
  return { result: undefined, data: undefined };
}

export const DownloadResult = {
  encode(message: DownloadResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      File.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DownloadResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDownloadResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = File.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DownloadResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? File.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: DownloadResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = File.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DownloadResult>, I>>(base?: I): DownloadResult {
    return DownloadResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DownloadResult>, I>>(object: I): DownloadResult {
    const message = createBaseDownloadResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null) ? File.fromPartial(object.data) : undefined;
    return message;
  },
};

function createBaseIntResult(): IntResult {
  return { result: undefined, data: 0 };
}

export const IntResult = {
  encode(message: IntResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== 0) {
      writer.uint32(16).int32(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.data = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.Number(object.data) : 0,
    };
  },

  toJSON(message: IntResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== 0) {
      obj.data = Math.round(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IntResult>, I>>(base?: I): IntResult {
    return IntResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntResult>, I>>(object: I): IntResult {
    const message = createBaseIntResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? 0;
    return message;
  },
};

function createBaseLongResult(): LongResult {
  return { result: undefined, data: 0 };
}

export const LongResult = {
  encode(message: LongResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== 0) {
      writer.uint32(16).int64(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LongResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.data = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LongResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.Number(object.data) : 0,
    };
  },

  toJSON(message: LongResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== 0) {
      obj.data = Math.round(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LongResult>, I>>(base?: I): LongResult {
    return LongResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongResult>, I>>(object: I): LongResult {
    const message = createBaseLongResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? 0;
    return message;
  },
};

function createBaseIntsResult(): IntsResult {
  return { result: undefined, data: [] };
}

export const IntsResult = {
  encode(message: IntsResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    writer.uint32(18).fork();
    for (const v of message.data) {
      writer.int32(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IntsResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntsResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag === 16) {
            message.data.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.data.push(reader.int32());
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IntsResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => globalThis.Number(e)) : [],
    };
  },

  toJSON(message: IntsResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => Math.round(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IntsResult>, I>>(base?: I): IntsResult {
    return IntsResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntsResult>, I>>(object: I): IntsResult {
    const message = createBaseIntsResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => e) || [];
    return message;
  },
};

function createBaseDoubleResult(): DoubleResult {
  return { result: undefined, data: 0 };
}

export const DoubleResult = {
  encode(message: DoubleResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== 0) {
      writer.uint32(17).double(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DoubleResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDoubleResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.data = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DoubleResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.Number(object.data) : 0,
    };
  },

  toJSON(message: DoubleResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== 0) {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DoubleResult>, I>>(base?: I): DoubleResult {
    return DoubleResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoubleResult>, I>>(object: I): DoubleResult {
    const message = createBaseDoubleResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? 0;
    return message;
  },
};

function createBaseIdResult(): IdResult {
  return { result: undefined, data: "" };
}

export const IdResult = {
  encode(message: IdResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== "") {
      writer.uint32(18).string(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.String(object.data) : "",
    };
  },

  toJSON(message: IdResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== "") {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdResult>, I>>(base?: I): IdResult {
    return IdResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdResult>, I>>(object: I): IdResult {
    const message = createBaseIdResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? "";
    return message;
  },
};

function createBaseIdsResult(): IdsResult {
  return { result: undefined, data: [] };
}

export const IdsResult = {
  encode(message: IdsResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdsResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdsResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdsResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: IdsResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdsResult>, I>>(base?: I): IdsResult {
    return IdsResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdsResult>, I>>(object: I): IdsResult {
    const message = createBaseIdsResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => e) || [];
    return message;
  },
};

function createBaseStringResult(): StringResult {
  return { result: undefined, data: "" };
}

export const StringResult = {
  encode(message: StringResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== "") {
      writer.uint32(18).string(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.String(object.data) : "",
    };
  },

  toJSON(message: StringResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== "") {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StringResult>, I>>(base?: I): StringResult {
    return StringResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringResult>, I>>(object: I): StringResult {
    const message = createBaseStringResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? "";
    return message;
  },
};

function createBaseStringsResult(): StringsResult {
  return { result: undefined, data: [] };
}

export const StringsResult = {
  encode(message: StringsResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StringsResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringsResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StringsResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: StringsResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StringsResult>, I>>(base?: I): StringsResult {
    return StringsResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringsResult>, I>>(object: I): StringsResult {
    const message = createBaseStringsResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => e) || [];
    return message;
  },
};

function createBaseBooleanResult(): BooleanResult {
  return { result: undefined, data: false };
}

export const BooleanResult = {
  encode(message: BooleanResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== false) {
      writer.uint32(16).bool(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BooleanResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBooleanResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.data = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BooleanResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? globalThis.Boolean(object.data) : false,
    };
  },

  toJSON(message: BooleanResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== false) {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BooleanResult>, I>>(base?: I): BooleanResult {
    return BooleanResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BooleanResult>, I>>(object: I): BooleanResult {
    const message = createBaseBooleanResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data ?? false;
    return message;
  },
};

function createBaseBooleansResult(): BooleansResult {
  return { result: undefined, data: [] };
}

export const BooleansResult = {
  encode(message: BooleansResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    writer.uint32(18).fork();
    for (const v of message.data) {
      writer.bool(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BooleansResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBooleansResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag === 16) {
            message.data.push(reader.bool());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.data.push(reader.bool());
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BooleansResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => globalThis.Boolean(e)) : [],
    };
  },

  toJSON(message: BooleansResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BooleansResult>, I>>(base?: I): BooleansResult {
    return BooleansResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BooleansResult>, I>>(object: I): BooleansResult {
    const message = createBaseBooleansResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => e) || [];
    return message;
  },
};

function createBaseIdNameResult(): IdNameResult {
  return { result: undefined, data: undefined };
}

export const IdNameResult = {
  encode(message: IdNameResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      IdNameModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdNameResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdNameResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = IdNameModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdNameResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? IdNameModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: IdNameResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = IdNameModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdNameResult>, I>>(base?: I): IdNameResult {
    return IdNameResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdNameResult>, I>>(object: I): IdNameResult {
    const message = createBaseIdNameResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? IdNameModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseNameValueResult(): NameValueResult {
  return { result: undefined, data: undefined };
}

export const NameValueResult = {
  encode(message: NameValueResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      NameValueModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): NameValueResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNameValueResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = NameValueModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NameValueResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? NameValueModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: NameValueResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = NameValueModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NameValueResult>, I>>(base?: I): NameValueResult {
    return NameValueResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NameValueResult>, I>>(object: I): NameValueResult {
    const message = createBaseNameValueResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? NameValueModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseNameValuesResult(): NameValuesResult {
  return { result: undefined, data: undefined };
}

export const NameValuesResult = {
  encode(message: NameValuesResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      NameValuesModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): NameValuesResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNameValuesResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = NameValuesModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NameValuesResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? NameValuesModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: NameValuesResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = NameValuesModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NameValuesResult>, I>>(base?: I): NameValuesResult {
    return NameValuesResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NameValuesResult>, I>>(object: I): NameValuesResult {
    const message = createBaseNameValuesResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? NameValuesModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseMapResult(): MapResult {
  return { result: undefined, data: {} };
}

export const MapResult = {
  encode(message: MapResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    Object.entries(message.data).forEach(([key, value]) => {
      MapResult_DataEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MapResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMapResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          const entry2 = MapResult_DataEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.data[entry2.key] = entry2.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MapResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isObject(object.data)
        ? Object.entries(object.data).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: MapResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data) {
      const entries = Object.entries(message.data);
      if (entries.length > 0) {
        obj.data = {};
        entries.forEach(([k, v]) => {
          obj.data[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MapResult>, I>>(base?: I): MapResult {
    return MapResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MapResult>, I>>(object: I): MapResult {
    const message = createBaseMapResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = Object.entries(object.data ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseMapResult_DataEntry(): MapResult_DataEntry {
  return { key: "", value: "" };
}

export const MapResult_DataEntry = {
  encode(message: MapResult_DataEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MapResult_DataEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMapResult_DataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MapResult_DataEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: MapResult_DataEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MapResult_DataEntry>, I>>(base?: I): MapResult_DataEntry {
    return MapResult_DataEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MapResult_DataEntry>, I>>(object: I): MapResult_DataEntry {
    const message = createBaseMapResult_DataEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseLogResult(): LogResult {
  return { result: undefined, data: [] };
}

export const LogResult = {
  encode(message: LogResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      LogModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LogResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLogResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(LogModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LogResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => LogModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: LogResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => LogModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LogResult>, I>>(base?: I): LogResult {
    return LogResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogResult>, I>>(object: I): LogResult {
    const message = createBaseLogResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => LogModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseImportResult(): ImportResult {
  return { result: undefined, data: undefined };
}

export const ImportResult = {
  encode(message: ImportResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      ImportModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ImportResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseImportResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = ImportModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ImportResult {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? ImportModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: ImportResult): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = ImportModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ImportResult>, I>>(base?: I): ImportResult {
    return ImportResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImportResult>, I>>(object: I): ImportResult {
    const message = createBaseImportResult();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? ImportModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseSortParam(): SortParam {
  return { orderBy: "", isAsc: false };
}

export const SortParam = {
  encode(message: SortParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderBy !== "") {
      writer.uint32(10).string(message.orderBy);
    }
    if (message.isAsc !== false) {
      writer.uint32(16).bool(message.isAsc);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SortParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSortParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.orderBy = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.isAsc = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SortParam {
    return {
      orderBy: isSet(object.orderBy) ? globalThis.String(object.orderBy) : "",
      isAsc: isSet(object.isAsc) ? globalThis.Boolean(object.isAsc) : false,
    };
  },

  toJSON(message: SortParam): unknown {
    const obj: any = {};
    if (message.orderBy !== "") {
      obj.orderBy = message.orderBy;
    }
    if (message.isAsc !== false) {
      obj.isAsc = message.isAsc;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SortParam>, I>>(base?: I): SortParam {
    return SortParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SortParam>, I>>(object: I): SortParam {
    const message = createBaseSortParam();
    message.orderBy = object.orderBy ?? "";
    message.isAsc = object.isAsc ?? false;
    return message;
  },
};

function createBaseIdNameModel(): IdNameModel {
  return { id: "", name: "" };
}

export const IdNameModel = {
  encode(message: IdNameModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IdNameModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdNameModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdNameModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: IdNameModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdNameModel>, I>>(base?: I): IdNameModel {
    return IdNameModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdNameModel>, I>>(object: I): IdNameModel {
    const message = createBaseIdNameModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseCodeNameModel(): CodeNameModel {
  return { code: "", name: "" };
}

export const CodeNameModel = {
  encode(message: CodeNameModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== "") {
      writer.uint32(10).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CodeNameModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCodeNameModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.code = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CodeNameModel {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: CodeNameModel): unknown {
    const obj: any = {};
    if (message.code !== "") {
      obj.code = message.code;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CodeNameModel>, I>>(base?: I): CodeNameModel {
    return CodeNameModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CodeNameModel>, I>>(object: I): CodeNameModel {
    const message = createBaseCodeNameModel();
    message.code = object.code ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseNameValueModel(): NameValueModel {
  return { name: "", value: "" };
}

export const NameValueModel = {
  encode(message: NameValueModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): NameValueModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNameValueModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NameValueModel {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: NameValueModel): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NameValueModel>, I>>(base?: I): NameValueModel {
    return NameValueModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NameValueModel>, I>>(object: I): NameValueModel {
    const message = createBaseNameValueModel();
    message.name = object.name ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseNameCodeModel(): NameCodeModel {
  return { name: "", code: 0 };
}

export const NameCodeModel = {
  encode(message: NameCodeModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.code !== 0) {
      writer.uint32(16).int32(message.code);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): NameCodeModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNameCodeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.code = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NameCodeModel {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
    };
  },

  toJSON(message: NameCodeModel): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.code !== 0) {
      obj.code = Math.round(message.code);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NameCodeModel>, I>>(base?: I): NameCodeModel {
    return NameCodeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NameCodeModel>, I>>(object: I): NameCodeModel {
    const message = createBaseNameCodeModel();
    message.name = object.name ?? "";
    message.code = object.code ?? 0;
    return message;
  },
};

function createBaseNameValuesModel(): NameValuesModel {
  return { name: "", values: [] };
}

export const NameValuesModel = {
  encode(message: NameValuesModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    for (const v of message.values) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): NameValuesModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNameValuesModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NameValuesModel {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: NameValuesModel): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.values?.length) {
      obj.values = message.values;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NameValuesModel>, I>>(base?: I): NameValuesModel {
    return NameValuesModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NameValuesModel>, I>>(object: I): NameValuesModel {
    const message = createBaseNameValuesModel();
    message.name = object.name ?? "";
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseLogModel(): LogModel {
  return { id: 0, action: "", remark: "", images: [], createBy: "", createTime: 0 };
}

export const LogModel = {
  encode(message: LogModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.action !== "") {
      writer.uint32(18).string(message.action);
    }
    if (message.remark !== "") {
      writer.uint32(26).string(message.remark);
    }
    for (const v of message.images) {
      writer.uint32(34).string(v!);
    }
    if (message.createBy !== "") {
      writer.uint32(42).string(message.createBy);
    }
    if (message.createTime !== 0) {
      writer.uint32(48).int64(message.createTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LogModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLogModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64() as Long);
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.action = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.remark = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.images.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.createBy = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.createTime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LogModel {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      action: isSet(object.action) ? globalThis.String(object.action) : "",
      remark: isSet(object.remark) ? globalThis.String(object.remark) : "",
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : [],
      createBy: isSet(object.createBy) ? globalThis.String(object.createBy) : "",
      createTime: isSet(object.createTime) ? globalThis.Number(object.createTime) : 0,
    };
  },

  toJSON(message: LogModel): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.action !== "") {
      obj.action = message.action;
    }
    if (message.remark !== "") {
      obj.remark = message.remark;
    }
    if (message.images?.length) {
      obj.images = message.images;
    }
    if (message.createBy !== "") {
      obj.createBy = message.createBy;
    }
    if (message.createTime !== 0) {
      obj.createTime = Math.round(message.createTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LogModel>, I>>(base?: I): LogModel {
    return LogModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogModel>, I>>(object: I): LogModel {
    const message = createBaseLogModel();
    message.id = object.id ?? 0;
    message.action = object.action ?? "";
    message.remark = object.remark ?? "";
    message.images = object.images?.map((e) => e) || [];
    message.createBy = object.createBy ?? "";
    message.createTime = object.createTime ?? 0;
    return message;
  },
};

function createBaseImportModel(): ImportModel {
  return { totalCount: 0, successCount: 0, failCount: 0, downloadUrl: "" };
}

export const ImportModel = {
  encode(message: ImportModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.totalCount !== 0) {
      writer.uint32(8).int32(message.totalCount);
    }
    if (message.successCount !== 0) {
      writer.uint32(16).int32(message.successCount);
    }
    if (message.failCount !== 0) {
      writer.uint32(24).int32(message.failCount);
    }
    if (message.downloadUrl !== "") {
      writer.uint32(34).string(message.downloadUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ImportModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseImportModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.successCount = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.failCount = reader.int32();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.downloadUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ImportModel {
    return {
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
      successCount: isSet(object.successCount) ? globalThis.Number(object.successCount) : 0,
      failCount: isSet(object.failCount) ? globalThis.Number(object.failCount) : 0,
      downloadUrl: isSet(object.downloadUrl) ? globalThis.String(object.downloadUrl) : "",
    };
  },

  toJSON(message: ImportModel): unknown {
    const obj: any = {};
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    if (message.successCount !== 0) {
      obj.successCount = Math.round(message.successCount);
    }
    if (message.failCount !== 0) {
      obj.failCount = Math.round(message.failCount);
    }
    if (message.downloadUrl !== "") {
      obj.downloadUrl = message.downloadUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ImportModel>, I>>(base?: I): ImportModel {
    return ImportModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImportModel>, I>>(object: I): ImportModel {
    const message = createBaseImportModel();
    message.totalCount = object.totalCount ?? 0;
    message.successCount = object.successCount ?? 0;
    message.failCount = object.failCount ?? 0;
    message.downloadUrl = object.downloadUrl ?? "";
    return message;
  },
};

function createBasePage(): Page {
  return { current: 0, size: 0, pages: 0, total: 0 };
}

export const Page = {
  encode(message: Page, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.current !== 0) {
      writer.uint32(8).int64(message.current);
    }
    if (message.size !== 0) {
      writer.uint32(16).int64(message.size);
    }
    if (message.pages !== 0) {
      writer.uint32(24).int64(message.pages);
    }
    if (message.total !== 0) {
      writer.uint32(32).int64(message.total);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Page {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.current = longToNumber(reader.int64() as Long);
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.size = longToNumber(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.pages = longToNumber(reader.int64() as Long);
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.total = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Page {
    return {
      current: isSet(object.current) ? globalThis.Number(object.current) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      pages: isSet(object.pages) ? globalThis.Number(object.pages) : 0,
      total: isSet(object.total) ? globalThis.Number(object.total) : 0,
    };
  },

  toJSON(message: Page): unknown {
    const obj: any = {};
    if (message.current !== 0) {
      obj.current = Math.round(message.current);
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    if (message.pages !== 0) {
      obj.pages = Math.round(message.pages);
    }
    if (message.total !== 0) {
      obj.total = Math.round(message.total);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Page>, I>>(base?: I): Page {
    return Page.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Page>, I>>(object: I): Page {
    const message = createBasePage();
    message.current = object.current ?? 0;
    message.size = object.size ?? 0;
    message.pages = object.pages ?? 0;
    message.total = object.total ?? 0;
    return message;
  },
};

function createBaseFileInfo(): FileInfo {
  return { fileName: "", fileUrl: "" };
}

export const FileInfo = {
  encode(message: FileInfo, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.fileName !== "") {
      writer.uint32(10).string(message.fileName);
    }
    if (message.fileUrl !== "") {
      writer.uint32(18).string(message.fileUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FileInfo {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFileInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.fileName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.fileUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FileInfo {
    return {
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : "",
      fileUrl: isSet(object.fileUrl) ? globalThis.String(object.fileUrl) : "",
    };
  },

  toJSON(message: FileInfo): unknown {
    const obj: any = {};
    if (message.fileName !== "") {
      obj.fileName = message.fileName;
    }
    if (message.fileUrl !== "") {
      obj.fileUrl = message.fileUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FileInfo>, I>>(base?: I): FileInfo {
    return FileInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FileInfo>, I>>(object: I): FileInfo {
    const message = createBaseFileInfo();
    message.fileName = object.fileName ?? "";
    message.fileUrl = object.fileUrl ?? "";
    return message;
  },
};

function createBaseFile(): File {
  return { header: undefined, data: new Uint8Array(0) };
}

export const File = {
  encode(message: File, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.header !== undefined) {
      FileHeader.encode(message.header, writer.uint32(10).fork()).ldelim();
    }
    if (message.data.length !== 0) {
      writer.uint32(18).bytes(message.data);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): File {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.header = FileHeader.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): File {
    return {
      header: isSet(object.header) ? FileHeader.fromJSON(object.header) : undefined,
      data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0),
    };
  },

  toJSON(message: File): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = FileHeader.toJSON(message.header);
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<File>, I>>(base?: I): File {
    return File.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<File>, I>>(object: I): File {
    const message = createBaseFile();
    message.header = (object.header !== undefined && object.header !== null)
      ? FileHeader.fromPartial(object.header)
      : undefined;
    message.data = object.data ?? new Uint8Array(0);
    return message;
  },
};

function createBaseFileHeader(): FileHeader {
  return { filename: "", contentType: "", mime: {} };
}

export const FileHeader = {
  encode(message: FileHeader, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.filename !== "") {
      writer.uint32(10).string(message.filename);
    }
    if (message.contentType !== "") {
      writer.uint32(18).string(message.contentType);
    }
    Object.entries(message.mime).forEach(([key, value]) => {
      FileHeader_MimeEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FileHeader {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFileHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.filename = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.contentType = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          const entry3 = FileHeader_MimeEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.mime[entry3.key] = entry3.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FileHeader {
    return {
      filename: isSet(object.filename) ? globalThis.String(object.filename) : "",
      contentType: isSet(object.contentType) ? globalThis.String(object.contentType) : "",
      mime: isObject(object.mime)
        ? Object.entries(object.mime).reduce<{ [key: string]: Strings }>((acc, [key, value]) => {
          acc[key] = Strings.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: FileHeader): unknown {
    const obj: any = {};
    if (message.filename !== "") {
      obj.filename = message.filename;
    }
    if (message.contentType !== "") {
      obj.contentType = message.contentType;
    }
    if (message.mime) {
      const entries = Object.entries(message.mime);
      if (entries.length > 0) {
        obj.mime = {};
        entries.forEach(([k, v]) => {
          obj.mime[k] = Strings.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FileHeader>, I>>(base?: I): FileHeader {
    return FileHeader.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FileHeader>, I>>(object: I): FileHeader {
    const message = createBaseFileHeader();
    message.filename = object.filename ?? "";
    message.contentType = object.contentType ?? "";
    message.mime = Object.entries(object.mime ?? {}).reduce<{ [key: string]: Strings }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Strings.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseFileHeader_MimeEntry(): FileHeader_MimeEntry {
  return { key: "", value: undefined };
}

export const FileHeader_MimeEntry = {
  encode(message: FileHeader_MimeEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      Strings.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FileHeader_MimeEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFileHeader_MimeEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = Strings.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FileHeader_MimeEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? Strings.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: FileHeader_MimeEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = Strings.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FileHeader_MimeEntry>, I>>(base?: I): FileHeader_MimeEntry {
    return FileHeader_MimeEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FileHeader_MimeEntry>, I>>(object: I): FileHeader_MimeEntry {
    const message = createBaseFileHeader_MimeEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? Strings.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseStrings(): Strings {
  return { value: [] };
}

export const Strings = {
  encode(message: Strings, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.value) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Strings {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStrings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.value.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Strings {
    return { value: globalThis.Array.isArray(object?.value) ? object.value.map((e: any) => globalThis.String(e)) : [] };
  },

  toJSON(message: Strings): unknown {
    const obj: any = {};
    if (message.value?.length) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Strings>, I>>(base?: I): Strings {
    return Strings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Strings>, I>>(object: I): Strings {
    const message = createBaseStrings();
    message.value = object.value?.map((e) => e) || [];
    return message;
  },
};

function createBaseWebapiOption(): WebapiOption {
  return { upload: false, download: false };
}

export const WebapiOption = {
  encode(message: WebapiOption, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.upload !== false) {
      writer.uint32(8).bool(message.upload);
    }
    if (message.download !== false) {
      writer.uint32(16).bool(message.download);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): WebapiOption {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWebapiOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.upload = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.download = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WebapiOption {
    return {
      upload: isSet(object.upload) ? globalThis.Boolean(object.upload) : false,
      download: isSet(object.download) ? globalThis.Boolean(object.download) : false,
    };
  },

  toJSON(message: WebapiOption): unknown {
    const obj: any = {};
    if (message.upload !== false) {
      obj.upload = message.upload;
    }
    if (message.download !== false) {
      obj.download = message.download;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WebapiOption>, I>>(base?: I): WebapiOption {
    return WebapiOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WebapiOption>, I>>(object: I): WebapiOption {
    const message = createBaseWebapiOption();
    message.upload = object.upload ?? false;
    message.download = object.download ?? false;
    return message;
  },
};

function createBaseMessageOption(): MessageOption {
  return { generated: false };
}

export const MessageOption = {
  encode(message: MessageOption, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.generated !== false) {
      writer.uint32(8).bool(message.generated);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MessageOption {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.generated = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MessageOption {
    return { generated: isSet(object.generated) ? globalThis.Boolean(object.generated) : false };
  },

  toJSON(message: MessageOption): unknown {
    const obj: any = {};
    if (message.generated !== false) {
      obj.generated = message.generated;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageOption>, I>>(base?: I): MessageOption {
    return MessageOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageOption>, I>>(object: I): MessageOption {
    const message = createBaseMessageOption();
    message.generated = object.generated ?? false;
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

function base64FromBytes(arr: Uint8Array): string {
  if ((globalThis as any).Buffer) {
    return globalThis.Buffer.from(arr).toString("base64");
  } else {
    const bin: string[] = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(""));
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
