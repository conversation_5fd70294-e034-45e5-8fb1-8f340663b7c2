<template>
  <div>
    <nav-header></nav-header>
    <!-- 悬浮按钮 PC以及移动端 -->
    <client-only>
      <div class="affix-middle">
        <img
          loading="lazy"
          alt="whatsapp"
          class="affix-icon whatsapp-icon"
          @click="onWhatsAppClick"
          src="@/assets/icons/common/whatsapp-icon-default.svg"
        />
      </div>
      <n-space vertical class="affix-bottom">
        <!-- 置顶按钮 -->
        <img
          loading="lazy"
          alt="返回顶部"
          class="affix-icon top-arrow-icon"
          v-if="showBackTop"
          @click="onBackTop"
          src="@/assets/icons/common/top-arrow.svg"
        />
      </n-space>
    </client-only>
    <!-- 内容 -->
    <slot />
    <nav-footer></nav-footer>
  </div>
</template>
<script setup lang="ts">
const showBackTop = ref(false);

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

async function onScrollBottom() {
  // 获取滚动容器已滚动的高度
  const scrollTop = window.scrollY;
  if (scrollTop > 600) {
    showBackTop.value = true;
  } else {
    showBackTop.value = false;
  }
}

function onBackTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}
</script>
<style scoped>
.na-affix-right {
  position: fixed;
  padding: 0rem 0.4rem;
  z-index: 1000;
}
.affix-bottom {
  position: fixed;
  z-index: 1000;
  right: 24px;
  bottom: 200px;
  transform: translateY(50%);
  cursor: pointer;
}

.affix-middle {
  position: fixed;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  cursor: pointer;
}

.affix-icon {
  width: 50px;
  height: 50px;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.top-arrow-icon:hover {
  transform: scale(1.1);
}

.whatsapp-icon:hover {
  box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.15);
  content: url("@/assets/icons/common/whatsapp-icon-hover.svg");
}
</style>
