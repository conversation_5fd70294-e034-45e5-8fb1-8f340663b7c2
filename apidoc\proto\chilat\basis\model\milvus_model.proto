syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

message MilvusShowCollectionsResult {
  common.Result result = 1;
  MilvusShowCollectionsModel data = 2;
}

message MilvusShowCollectionsModel {
  string collectionName = 1;
  string databaseName = 2;
  string description = 3;
  int64 numOfPartitions = 4;
  repeated string fieldNames = 5;
  repeated string vectorFieldNames = 6;
  string primaryFieldName = 7;
  bool enableDynamicField = 8;
  bool autoID = 9;
  int64 createTime = 11;
}

message MilvusGetAllIdsResult {
  common.Result result = 1;
  MilvusGetAllIdsModel data = 2;
}

message MilvusGetAllIdsModel {
  repeated string ids = 1;
}