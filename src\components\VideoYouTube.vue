/** * YouTube 视频播放器 (兼容版) * * 技术: video.js + videojs-youtube * 功能: *
- 支持 YouTube 链接，支持封面图 * - PC端: 用户交互后可自动播放 * - 移动端:
用户交互后不支持自动播放 */

<template>
  <component
    v-if="VideoPlayerComponent"
    :is="VideoPlayerComponent"
    ref="videoPlayer"
    class="video-js vjs-default-skin"
    :options="videoOptions"
    :width="isMobile ? (props.width / 50) * resetFontSize : props.width"
    :height="isMobile ? (props.height / 50) * resetFontSize : props.height"
    :onPlay="onVideoPlay"
    :onEnded="onVideoEnd"
    :onError="onVideoError"
  ></component>
</template>

<script setup lang="ts">
import "video.js/dist/video-js.css";
import "videojs-youtube";

const route = useRoute();

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const props = defineProps({
  youtubeId: {
    type: String,
    required: true,
  },
  width: {
    type: Number,
    default: 360,
  },
  height: {
    type: Number,
    default: 180,
  },
  poster: {
    type: String,
    default: "",
  },
  serial: {
    type: Number,
    default: 1,
  },
  title: {
    type: String,
    default: "",
  },
  titleCh: {
    type: String,
    default: "",
  },
  autoplay: {
    type: Boolean,
    default: false,
  },
});

const videoOptions = ref({
  techOrder: ["youtube"],
  sources: [
    {
      type: "video/youtube",
      src: `https://www.youtube.com/watch?v=${props.youtubeId}`,
    },
  ],
  controls: true,
  preload: "none",
  poster: props.poster || undefined,
  html5: {
    nativePictureInPicture: false,
  },
  autoplay: props.autoplay,
});

const VideoPlayerComponent = ref(null);
const player = ref<any>(null);
const isPlaying = ref(false);

onMounted(() => {
  import("@videojs-player/vue").then((module) => {
    VideoPlayerComponent.value = module.VideoPlayer;
  });

  player.value = videoOptions.value;
});

onBeforeUnmount(() => {
  if (player.value && player.value.dispose) {
    player.value.dispose();
  }
});

function onVideoPlay() {
  if (!isPlaying.value) {
    isPlaying.value = true;
    if (props.titleCh || props.title) {
      window?.MyStat?.addPageEvent(
        "video_play_start",
        `${props.titleCh ?? props.title}播放开始`,
      );
    }
  }
}

function onVideoEnd() {
  isPlaying.value = false;
  if (props.titleCh || props.title) {
    window?.MyStat?.addPageEvent(
      "video_play_finish",
      `${props.titleCh ?? props.title}播放结束`,
    );
  }
}

function onVideoError(error: any) {
  const errorMessage = error.message || "未知错误";
  if (props.titleCh || props.title) {
    window?.MyStat?.addPageEvent(
      "video_play_error",
      `${props.titleCh ?? props.title}播放失败：${errorMessage}`,
    );
  }
}
</script>

<style scoped></style>
