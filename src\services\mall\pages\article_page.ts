/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { ArticleCategoryTreeResp } from "../../chilat/support/model/article_category_model";
import { ArticleResp, GetBlogListResp, ListArticleResp, SearchArticleListResp } from "./model/article_page_model";
import {
  ArticleDetailParam,
  GetBlogListParam,
  ListArticleCategoryParam,
  ListArticleParam,
  SearchArticleListParam,
} from "./param/article_page_param";

export const protobufPackage = "mall.pages";

export interface ArticlePage {
  /** 查询栏目 */
  listArticleCategory(request: ListArticleCategoryParam): Promise<ArticleCategoryTreeResp>;
  /** 根据栏目查询文章 */
  listArticleByCategoryId(request: ListArticleParam): Promise<ListArticleResp>;
  /** 获取BLOG列表 */
  getBlogList(request: GetBlogListParam): Promise<GetBlogListResp>;
  /** 查询文章详情 */
  articleDetail(request: ArticleDetailParam): Promise<ArticleResp>;
  /** 根据筛选条件查询文章列表 */
  searchArticleList(request: SearchArticleListParam): Promise<SearchArticleListResp>;
}

export const ArticlePageServiceName = "mall.pages.ArticlePage";
export class ArticlePageClientImpl implements ArticlePage {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || ArticlePageServiceName;
    this.rpc = rpc;
    this.listArticleCategory = this.listArticleCategory.bind(this);
    this.listArticleByCategoryId = this.listArticleByCategoryId.bind(this);
    this.getBlogList = this.getBlogList.bind(this);
    this.articleDetail = this.articleDetail.bind(this);
    this.searchArticleList = this.searchArticleList.bind(this);
  }
  listArticleCategory(request: ListArticleCategoryParam): Promise<ArticleCategoryTreeResp> {
    const data = ListArticleCategoryParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "listArticleCategory", data);
    return promise.then((data) => ArticleCategoryTreeResp.decode(_m0.Reader.create(data)));
  }

  listArticleByCategoryId(request: ListArticleParam): Promise<ListArticleResp> {
    const data = ListArticleParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "listArticleByCategoryId", data);
    return promise.then((data) => ListArticleResp.decode(_m0.Reader.create(data)));
  }

  getBlogList(request: GetBlogListParam): Promise<GetBlogListResp> {
    const data = GetBlogListParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBlogList", data);
    return promise.then((data) => GetBlogListResp.decode(_m0.Reader.create(data)));
  }

  articleDetail(request: ArticleDetailParam): Promise<ArticleResp> {
    const data = ArticleDetailParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "articleDetail", data);
    return promise.then((data) => ArticleResp.decode(_m0.Reader.create(data)));
  }

  searchArticleList(request: SearchArticleListParam): Promise<SearchArticleListResp> {
    const data = SearchArticleListParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "searchArticleList", data);
    return promise.then((data) => SearchArticleListResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
