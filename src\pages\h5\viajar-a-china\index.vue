<template>
  <div class="page-container">
    <div class="page-header">
      <div class="w-full px-[0.4rem] py-[0.44rem] mx-auto">
        <div class="relative z-2">
          <div class="w-[6.8rem] text-[0.76rem] leading-[1rem] font-medium mt-[1.16rem]">
            Conozca el<br />centro de la<br />excelencia<br />en la fabricación
          </div>

          <div class="w-[6.8rem] text-[0.4rem] leading-[0.68rem] mt-[0.72rem]">
            Embárquese en<br />un viaje transformador<br />de asesoramiento a China
          </div>
        </div>
      </div>
    </div>
    <div class="w-full text-[#333] px-[0.4rem] pb-[3.08rem]">
      <div class="w-full mt-[0.8rem] text-center">
        <div class="text-[0.56rem] leading-[0.68rem]">
          ¿Es necesario para usted hacer un viaje a China para importar?
        </div>
        <div class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.32rem]">
          <div>
            ¡Absolutamente! Imagínese tener reuniones cara a cara con proveedores y establecer relaciones a largo plazo.
          </div>
          <div>Un viaje a China puede ser una experiencia valiosa para las empresas que buscan:</div>
        </div>
        <div class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.6rem] mx-auto"></div>
        <div class="w-full mt-[0.52rem] flex flex-col">
          <div
            v-for="(item, index) in visitChinaData"
            :key="index"
            class="w-full flex flex-col items-center mt-[0.68rem]"
          >
            <img loading="lazy" :src="item.imgUrl" alt="viajar a china" class="w-[0.8rem]" />
            <div class="text-[0.36rem] leading-[0.36rem] mt-[0.28rem]">
              {{ item.title }}
            </div>
            <div class="text-[0.28rem] leading-[0.36rem] text-[#7F7F7F] mt-[0.2rem]">
              {{ item.desc }}
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem]"
          >
            <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
      <div class="w-full mt-[1.6rem]">
        <div class="text-[0.56rem] leading-[0.68rem] text-center">¿De qué nos ocuparemos?</div>
        <div class="w-full text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] text-center mt-[0.32rem]">
          Le brindamos un servicio integral para compras en China, ayudándole a comprar productos de buena calidad con
          mejor precio en China y resolviendo problemas comunes que encuentre durante el proceso de compra.
        </div>
        <div class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.6rem] mx-auto"></div>
        <div class="flex flex-col mt-[0.96rem]">
          <div
            v-for="(item, index) in visitProcData"
            :key="index"
            class="relative w-full h-[7.64rem] bg-white border border-[#333] rounded-[0.4rem] pt-[4.4rem] pb-[0.32rem] px-[0.24rem] mt-[0.24rem]"
          >
            <img loading="lazy" :src="item.imgUrl" alt="viajar a china" class="absolute top-0 left-0" />
            <div class="text-[0.4rem] leading-[0.48rem] text-center">
              {{ item.title }}
            </div>
            <n-space
              vertical
              :style="{ gap: '0.08rem 0' }"
              class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.24rem]"
            >
              <div v-for="desc in item.descData" :key="desc">
                {{ desc }}
              </div>
            </n-space>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem]"
          >
            <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
      <div class="w-full mt-[1.6rem]">
        <div class="flex items-center justify-center">
          <img loading="lazy" :src="rightArrow" alt="viajar a china" class="mr-[0.16rem] w-[0.16rem]" />
          <span class="text-[0.32rem] leading-[0.32rem] text-[#7F7F7F]">SI VIENE A CHINA</span>
        </div>
        <div class="text-[0.56rem] leading-[0.68rem] mt-[0.32rem] text-center">¿Cómo trabajamos?</div>
        <div class="mt-[1.2rem]">
          <div class="w-[6.7rem] h-[3.76rem] !overflow-hidden">
            <video-you-tube
              :width="335"
              :height="188.438"
              :key="pageData.activatedWorkVideo?.id"
              :poster="pageData.activatedWorkVideo.poster"
              :youtubeId="pageData.activatedWorkVideo?.id"
              :title="pageData.activatedWorkVideo?.title"
              :titleCh="pageData.activatedWorkVideo?.titleCh"
            ></video-you-tube>
          </div>
          <n-space vertical :style="{ gap: '0.44rem 0' }" class="mt-[0.6rem]">
            <div
              v-for="item in workVideoData"
              :key="item.id"
              class="flex items-center cursor-pointer"
              @click="onPlayVideo(item)"
            >
              <img
                loading="lazy"
                :src="item.id === pageData.activatedWorkVideo?.id ? mobileVideoing : mobileVideo"
                alt="viajar a china"
                class="mr-[0.16rem]"
              />
              <div
                :class="item.id === pageData.activatedWorkVideo?.id ? '!text-[#e50113]' : ''"
                class="text-[0.3rem] leading-[0.32rem] text-medium"
              >
                {{ item.title }}
              </div>
            </div>
          </n-space>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#fff"
            text-color="#e50113"
            @click="onWhatsAppClick"
            class="global-navigation-btn mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem]"
          >
            <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> ¡Consultar ahora!</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="bg-[#E50113] text-[#FFF] relative z-1">
      <div class="w-full relative px-[0.4rem] pt-[3.16rem] pb-[1.4rem]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[6.7rem] absolute right-[0.4rem] top-[-1rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[0.56rem] leading-[0.68rem] text-center">¿Por qué nosotros?</div>
        <div class="w-[1rem] h-[0.06rem] bg-[#fff] mx-auto mt-[0.32rem]"></div>
        <div class="flex flex-col mt-[0.48rem]">
          <div
            v-for="(item, index) in whyUsData"
            :key="index"
            class="w-full pb-[0.52rem] border-b-1 border-[#FFF] mt-[0.52rem]"
          >
            <div class="flex text-[0.44rem] leading-[0.44rem] font-medium">
              <img loading="lazy" :src="rightArrowWhite" alt="viajar a china" class="mr-[0.2rem] w-[0.16rem]" />
              0{{ index + 1 }}
            </div>
            <div class="text-[0.36rem] leading-[0.44rem] font-medium mt-[0.4rem]" v-html="item.title"></div>
            <div class="text-[0.28rem] leading-[0.36rem] text-[#F2F2F2] mt-[0.24rem]">
              {{ item.desc }}
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <n-button
            size="large"
            color="#e50113"
            text-color="#fff"
            @click="onWhatsAppClick"
            class="mx-auto rounded-[10rem] mt-[1rem] h-[0.96rem] whatsapp-btn"
          >
            <span class="text-[0.32rem] leading-[0.4rem] px-[0.4rem]"> Comience a abastecerse ahora</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="bg-[#F7F7F7]">
      <div class="w-full relative flex flex-col">
        <div class="w-[7.84rem] h-[7.84rem] rounded-full bg-white absolute bottom-[-3.88rem] right-[0.92rem]"></div>
        <div class="w-full overflow-hidden relative z-1">
          <div class="text-[0.56rem] leading-[0.68rem] text-center mt-[1.6rem] px-[0.4rem]">
            Conozca a las personas que eligieron chilat
          </div>
          <div class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.32rem] mx-auto"></div>
          <div class="w-full flex my-[1.2rem] pb-[3.1rem]">
            <n-carousel
              :space-between="10"
              :loop="false"
              draggable
              slides-per-view="auto"
              centered-slides
              show-arrow
              :on-update:current-index="onUpdateCurrentIndex"
            >
              <n-carousel-item
                class="!w-[5.76rem] !h-[3.24rem] rounded-[0.4rem] relative overflow-hidden flex-shrink-0"
                v-for="(video, index) in userVideoData"
                :key="video.id"
                @click="onOpenVideo(video)"
              >
                <div class="w-[5.76rem] h-[3.24rem]">
                  <n-image lazy preview-disabled :src="video.poster" class="img" object-fit="cover" />
                  <img
                    loading="lazy"
                    :src="videoPlay"
                    alt="viajar a china"
                    class="w-[1rem] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
                  />
                </div>
              </n-carousel-item>
              <template #arrow="{ prev, next }">
                <div class="custom-arrow">
                  <icon-card
                    @click="prev"
                    name="fe:arrow-left"
                    size="26"
                    :color="pageData.currentCarouselIndex === 0 ? '#D9D9D9' : '#e50113'"
                    class="mr-[0.72rem]"
                  >
                  </icon-card>
                  <icon-card
                    @click="next"
                    name="fe:arrow-right"
                    size="26"
                    :color="pageData.currentCarouselIndex === userVideoData.length - 1 ? '#D9D9D9' : '#e50113'"
                  >
                  </icon-card>
                </div>
              </template>
              <template #dots="{ total, currentIndex, to }">
                <ul class="custom-dots">
                  <li v-for="index of total" :key="index" :class="{ ['is-active']: index - 1 <= currentIndex }"></li>
                </ul>
              </template>
            </n-carousel>
          </div>
        </div>
      </div>
    </div>
    <MobileWhatsAppContact />
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import rightArrow from "@/assets/icons/viajar-a-china/rightArrow.svg";
import rightArrowWhite from "@/assets/icons/viajar-a-china/rightArrowWhite.svg";
import mobileVideoing from "@/assets/icons/como-trabajarnos/mobile-videoing.svg";
import mobileVideo from "@/assets/icons/como-trabajarnos/mobile-video.svg";
import videoPlay from "@/assets/icons/viajar-a-china/videoPlay.svg";
import money from "@/assets/icons/viajar-a-china/money.svg";
import shopping from "@/assets/icons/viajar-a-china/shopping.svg";
import find from "@/assets/icons/viajar-a-china/find.svg";
import cooperation from "@/assets/icons/viajar-a-china/cooperation.svg";
import travelTips from "@/assets/icons/viajar-a-china/travelTips.png";
import accommodation from "@/assets/icons/viajar-a-china/accommodation.png";
import expertAdvice from "@/assets/icons/viajar-a-china/expertAdvice.png";
import interpreters from "@/assets/icons/viajar-a-china/interpreters.png";
import orderQualityControl from "@/assets/icons/viajar-a-china/orderQualityControl.png";
import logisticsAssistance from "@/assets/icons/viajar-a-china/logisticsAssistance.png";
import workVideoPoster1 from "@/assets/icons/viajar-a-china/workVideoPoster1.png";
import workVideoPoster2 from "@/assets/icons/viajar-a-china/workVideoPoster2.png";
import workVideoPoster3 from "@/assets/icons/viajar-a-china/workVideoPoster3.png";
import workVideoPoster4 from "@/assets/icons/viajar-a-china/workVideoPoster4.png";
import workVideoPoster5 from "@/assets/icons/viajar-a-china/workVideoPoster5.png";

import colombiaVideoPoster2 from "@/assets/icons/vip/colombia-video-poster2.jpg";
import argentinaVideoPoster3 from "@/assets/icons/vip/argentina-video-poster3.jpg";
import argentinaVideoPoster4 from "@/assets/icons/vip/argentina-video-poster4.jpg";
import argentinaVideoPoster5 from "@/assets/icons/vip/argentina-video-poster5.jpg";
import chileVideoPoster from "@/assets/icons/vip/chile-video-poster.jpg";
import hondurasVideoPoster from "@/assets/icons/vip/honduras-video-poster.jpg";

useHead({
  title: "Viajar a China - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/viajar-a-china/`,
    },
  ],
});

const videoModalRef = ref<any>(null);

const visitChinaData = [
  {
    imgUrl: money,
    title: "Precio competitivo",
    desc: "Los eficientes procesos de producción y las economías de escala de China permiten tener precios muy competitivos, pudiendo obtener los mejores márgenes de beneficio.",
  },
  {
    imgUrl: shopping,
    title: "Amplia gama de productos",
    desc: "Ya sea que esté buscando productos electrónicos, textiles, maquinaria o cualquier otro producto, China cuenta con una amplia gama y gran capacidad de producción de bienes de capital.",
  },
  {
    imgUrl: find,
    title: "Garantía de calidad",
    desc: "Inspeccione personalmente las fábricas y los procesos de producción para garantizar buena calidad y cumplir con sus estándares.",
  },
  {
    imgUrl: cooperation,
    title: "Construir relaciones comerciales",
    desc: "Las reuniones cara a cara con proveedores le ayudarán a establecer confianza y relaciones comerciales más sólidas, mejorando la comunicación y la cooperación.",
  },
];

const visitProcData = [
  {
    imgUrl: travelTips,
    title: "Consejos de viaje",
    descData: [
      "Antes de su viaje a China, investigaremos cada uno de los mercados mayoristas que desee visitar segun los requisitos de sus productos.",
      "De esta manera, priorizamoslos lugares que necesita paraasegurar el uso más productivode su tiempo.",
    ],
  },
  {
    imgUrl: accommodation,
    title: "Alojamiento",
    descData: [
      "Arreglos de alojamientoconfortable durante el viajepara asegurar que losparticipantes tengan unaestancia relajante y placentera.",
    ],
  },
  {
    imgUrl: expertAdvice,
    title: "Especialistas asesoramiento",
    descData: [
      "Expertos profesionales enasesoramiento que lesacompañan al grupo, brindandoorientación, respondiendopreguntas y ayudando con elproceso de asesoramientodurante todo el viaje.",
    ],
  },
  {
    imgUrl: interpreters,
    title: "Intérpretes",
    descData: [
      "Las barreras del idioma puedenser un desafio, pero la inclusiónde intérpretes de hablahispana, Garantiza unacomunicación fuida durante lasvisitas al mercado mayorista ylas reuniones con proveedores.",
    ],
  },
  {
    imgUrl: orderQualityControl,
    title: "Control de pedidos y calidad",
    descData: [
      "Realizar un seguimiento detodo el proceso de producciónpara garantizar que la entregay la calidad del productocumplan con sus estándares.",
    ],
  },
  {
    imgUrl: logisticsAssistance,
    title: "Asistencia Logistica",
    descData: [
      "En cuanto a los envos,brindamos asesoramiento entodo lo que tenga que ver conHetes, tramites aduaneros, etc.",
      "Nos ocupamos de todos losaspectos logisticos paraayudarle a navegar lascomplejidades del comerciointernacional.",
    ],
  },
];

const workVideoData = [
  {
    id: "b7X9D3BLvMc",
    poster: workVideoPoster1,
    title: "Emitir carta de invitación/reserva de hotel",
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: workVideoPoster2,
    title: "Recibir en el aeropuerto y visita al mercado",
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "wWtOhkPDg5A",
    poster: workVideoPoster3,
    title: "Realizar pedidos y seguimiento de producción",
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "GN6F6oLssUw",
    poster: workVideoPoster4,
    title: "Recibir los productos y hacer una inspección de calidad",
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: workVideoPoster5,
    title: "Cargar el contenedor y organizar los documentos de envío",
    titleCh: "5.装载集装箱并组织运输",
  },
];

const userVideoData = [
  {
    id: "bdfbqoK-Z3w",
    poster: colombiaVideoPoster2,
    titleCh: "1.我们的客户对我们的看法15",
  },
  {
    id: "h7-0oqtZGTU",
    poster: argentinaVideoPoster3,
    titleCh: "2.我们的客户对我们的看法16",
  },
  {
    id: "pzKmAInWpRo",
    poster: argentinaVideoPoster4,
    titleCh: "3.我们的客户对我们的看法17",
  },
  {
    id: "meWjJ7gWg8U",
    poster: argentinaVideoPoster5,
    titleCh: "4.我们的客户对我们的看法18",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: chileVideoPoster,
    titleCh: "5.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: hondurasVideoPoster,
    titleCh: "6.我们的客户对我们的看法6",
  },
];

const whyUsData = [
  {
    title: "CHILAT es una marca muy conocida en el mercado de América Latina",
    desc: "Con más de 20 años de servicio dedicados al comercio entre China y América Latina, entendemos mejor sus necesidades.",
  },
  {
    title: "Equipo <br/>experimentado",
    desc: "Contamos con más de 50 empleados comerciales de habla hispana, el 80% de ellos con más de 3 años de servicio, lo que nos brinda una amplia experiencia.",
  },
  {
    title: "Abundantes recursos de las fabricas",
    desc: "Con más de 2000 proveedores principales, garantizamos una perfecta coordinación de la producción.",
  },
  {
    title: "Equipo de aliados en múltiples países en América Latina",
    desc: "Contamos con agencias asociadas en varios países de América Latina que pueden brindarle soluciones localizadas.",
  },
];

const pageData = reactive(<any>{
  activatedWorkVideo: workVideoData[0],
  currentCarouselIndex: 0,
});

function onPlayVideo(val: any) {
  pageData.activatedWorkVideo = val;
}

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

function onUpdateCurrentIndex(index: any) {
  pageData.currentCarouselIndex = index;
}
</script>

<style scoped lang="scss">
.page-container {
  height: auto;
  min-height: 100vh;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 13.16rem;
  background-size: cover;
  color: #fff;
  background-image: url("@/assets/icons/viajar-a-china/svipVisitMobileBg.png");
  background-repeat: no-repeat;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(21, 27, 35, 0.5);
    z-index: 1;
  }
}

.n-carousel {
  overflow: visible;
}
.custom-arrow {
  display: flex;
  position: absolute;
  bottom: -1.6rem;
  right: 0.2rem;
  z-index: 10;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: -1.4rem;
  left: 0.4rem;
  border-radius: 0.08rem;
  background-color: #d9d9d9;
}

.custom-dots li {
  display: inline-block;
  width: 0.746rem;
  max-width: none;
  height: 0.04rem;
  background-color: #d9d9d9;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
}

.custom-dots li.is-active {
  height: 0.06rem;
  background: #e50113;
}
.vjs-poster img {
  object-fit: none;
  border-radius: 0.24rem !important;
}
.whatsapp-btn {
  border: 0.02rem solid #fff;
}
</style>
