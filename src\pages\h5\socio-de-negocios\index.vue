<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[0.4rem] py-[0.6rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">Socio de negocios</div>
    </div>
    <div class="w-full flex mt-[0.8rem] pb-[1.8rem] px-[0.4rem]">
      <n-carousel
        :space-between="10"
        :loop="false"
        draggable
        slides-per-view="auto"
        show-arrow
        :on-update:current-index="onUpdateCurrentIndex"
      >
        <n-carousel-item
          class="!w-[5.76rem] rounded-[0.08rem] relative overflow-hidden flex-shrink-0"
          v-for="(video, index) in partners"
          :key="index"
        >
          <div>
            <div class="w-[5.76rem] h-[5.76rem] rounded-[0.12rem] overflow-hidden">
              <video-you-tube
                :width="288"
                :height="288"
                :youtubeId="video.youtubeId"
                :titleCh="video.titleCh"
                :poster="video.poster"
              ></video-you-tube>
            </div>
            <div class="text-[0.36rem] leading-[0.36rem] mt-[0.4rem]">
              {{ video.title }}
            </div>
          </div>
        </n-carousel-item>
        <template #arrow="{ prev, next }">
          <div class="custom-arrow">
            <icon-card
              @click="prev"
              name="fe:arrow-left"
              size="26"
              :color="pageData.currentCarouselIndex === 0 ? '#D9D9D9' : '#e50113'"
              class="mr-[0.6rem]"
            >
            </icon-card>
            <icon-card
              @click="next"
              name="fe:arrow-right"
              size="26"
              :color="pageData.currentCarouselIndex === partners.length - 1 ? '#D9D9D9' : '#e50113'"
            >
            </icon-card>
          </div>
        </template>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li v-for="index of total" :key="index" :class="{ ['is-active']: index - 1 <= currentIndex }"></li>
          </ul>
        </template>
      </n-carousel>
    </div>
    <div class="pt-[1.12rem] pb-[1rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Socio de negocios</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7f7f7f] mt-[0.52rem]">
        Conviértase en un agente de Chilat en su país y ayude a más importadores a disfrutar del mejor servicio
        proporcionado por el equipo de Chilat.
      </div>
    </div>
    <div class="pt-[1.6rem] pb-[2.4rem] bg-[#FAFAFA] px-[0.4rem] text-[0.32rem] leading-[0.48rem]">
      <div class="text-[0.56rem] leading-[0.68rem]">Oportunidad de negocio</div>
      <div class="text-[#7f7f7f] mt-[0.72rem]">
        Con el rápido crecimiento de Chilat, cada vez más empresas y personas desean unirse a Chilat, convertirse en el
        agente local autorizado de Chilat y promover los servicios de Chilat a más importadores.
      </div>
      <div class="text-[#7f7f7f] mt-[0.6rem]">
        <span class="text-[#e50113]">*</span> Si está involucrado en importación y exportación o en la industria de la
        logística internacional, y conoce a muchos importadores.
      </div>
      <div class="text-[#7f7f7f] mt-[0.16rem]">
        <span class="text-[#e50113]">*</span> Si está interesado en llevar al equipo a comprar en China.
      </div>
      <div class="mt-[0.6rem]">
        Tiene la oportunidad de convertirse en un agente autorizado de Chilat. Puede recibir una comisión generosa y
        continua simplemente promoviendo nuestros servicios a los importadores locales.
      </div>
      <div class="mt-[0.16rem]">
        Mientras ayuda a otros, ¡sea también exitoso! Si está interesado en este negocio, póngase en contacto con el
        fundador
        <span class="text-[#e50113]">Mindy +8619057493913</span> (Whatsapp) para obtener más información.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import argentinaVideoPoster from "@/assets/icons/socio-de-negocios/argentina-video-poster.jpg";
import hondurasVideoPoster from "@/assets/icons/socio-de-negocios/honduras-video-poster.jpg";
import chileVideoPoster from "@/assets/icons/socio-de-negocios/chile-video-poster.jpg";
import uruguayVideoPoster from "@/assets/icons/socio-de-negocios/uruguay-video-poster.jpg";
import argentinaVideoPoster2 from "@/assets/icons/socio-de-negocios/argentina-video-poster2.jpg";
import argentinaVideoPoster3 from "@/assets/icons/socio-de-negocios/argentina-video-poster3.jpg";

useHead({
  title: "SOCIO DE NEGOCIOS | Oportunidad de negocio - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/socio-de-negocios/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { link: "/h5/socio-de-negocios", text: "Socio de negocios" },
];

const partners = [
  {
    youtubeId: "TROzVaB3Lr0",
    titleCh: "1.我们的客户对我们的看法7",
    poster: argentinaVideoPoster,
    title: "El socio en Argentina",
  },
  {
    youtubeId: "4FVIz0PvEcE",
    titleCh: "2.我们的客户对我们的看法5",
    poster: hondurasVideoPoster,
    title: "El socio en Honduras",
  },
  {
    youtubeId: "f6Zzt70urW8",
    titleCh: "3.我们的客户对我们的看法1",
    poster: chileVideoPoster,
    title: "El socio en Chile",
  },

  {
    youtubeId: "o870by8XanQ",
    titleCh: "4.我们的客户对我们的看法3",
    poster: uruguayVideoPoster,
    title: "El socio en Uruguay",
  },
  {
    youtubeId: "md8vgsFlKHY",
    titleCh: "5.我们的客户对我们的看法13",
    poster: argentinaVideoPoster2,
    title: "El socio en Argentina",
  },
  {
    youtubeId: "Y1Y8RTXV1FU",
    titleCh: "6.我们的客户对我们的看法14",
    poster: argentinaVideoPoster3,
    title: "El socio en Argentina",
  },
];

const pageData = reactive(<any>{
  currentCarouselIndex: 0,
});

function onUpdateCurrentIndex(index: any) {
  pageData.currentCarouselIndex = index;
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/socio-de-negocios/mobile-header-bg.png");
  background-repeat: no-repeat;
}
.n-carousel {
  overflow: visible;
}

.custom-arrow {
  display: flex;
  position: absolute;
  bottom: -1.38rem;
  right: 0rem;
  z-index: 10;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: -1.18rem;
  left: 0rem;
  border-radius: 0.08rem;
  background-color: #d9d9d9;
}

.custom-dots li {
  display: inline-block;
  width: 0.76rem;
  max-width: none;
  height: 0.02rem;
  background-color: #d9d9d9;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
}

.custom-dots li.is-active {
  height: 0.06rem;
  background: #e50113;
}
</style>
