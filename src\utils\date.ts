/**
 * 时间戳转日期
 * @param ts - 时间戳
 */
export function timezoneFormat(ts: number = 0) {
  if (!ts) {
    return "";
  }
  const date: Date = new Date(ts - 50400000);

  return `${`0${date.getDate()}`.slice(-2)}/${`0${date.getMonth() + 1}`.slice(
    -2,
  )}/${date.getFullYear()} ${`0${date.getHours()}`.slice(-2)}:${`0${date.getMinutes()}`.slice(
    -2,
  )}:${`0${date.getSeconds()}`.slice(-2)}`;
}

// 时间戳转日期 转北京时间 根据当前时区转时间
export function timeFormatByZone(
  ts: number = 0,
  showHour: boolean = true,
  showMinute: boolean = true,
  showSeconds: boolean = true,
) {
  if (!ts) {
    return null;
  }
  const date = new Date(ts);
  // 基本日期格式
  let formattedDate = `${date.getFullYear()}-${`0${date.getMonth() + 1}`.slice(-2)}-${`0${date.getDate()}`.slice(-2)} `;
  // 如果需要显示时，添加时
  if (showHour) {
    formattedDate = `${formattedDate}:${`0${date.getHours()}`.slice(-2)}`;
  }
  // 如果需要显示分，添加分
  if (showMinute) {
    formattedDate = `${formattedDate}:${`0${date.getMinutes()}`.slice(-2)}`;
  }

  // 如果需要显示秒，添加秒
  if (showSeconds) {
    formattedDate = `${formattedDate}:${`0${date.getSeconds()}`.slice(-2)}`;
  }

  // 不显示秒
  return formattedDate;
}
