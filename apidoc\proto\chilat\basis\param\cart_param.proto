syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";

message MidCartQueryParam {
    string token = 1; //前端无需传该值
    string goodsId = 2; //商品id
    int32 siteId = 3; //站点ID
}

message MidCartAddParam {
    string token = 10; //前端无需传该值
    string goodsId = 20; // 商品ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
    string routeId = 40; // 线路ID
    repeated MidCartSkuAddParam skus = 50; //sku列表
}

message MidCartSkuAddParam {
    string skuId = 1; // 商品SKU ID
    int32 quantity = 2; // 数量
    string spm = 3; //SPM跟踪码（在商详页中的SKU列表中添加到购物车，从location.href中取spm参数，参考代码：window.MyStat.getLocationParam('spm')）
}

message MidCartUpdateParam {
    string token = 1; //前端无需传该值
    string skuId = 2; // 商品SKU ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
    int32 quantity = 3; // 数量
    bool selected = 4; // 是否选中
    string updatedSkuId = 5; // 更新后的商品SKU ID
    string goodsId = 6; // 商品ID
    bool selectedAll = 7; // 全部选中
}

message MidCartRemoveParam {
    string token = 1; //前端无需传该值
    string skuId = 2; // 商品SKU ID
    string goodsId = 3; // 商品ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
}

//购物车合并参数
message MidMergeCartParam {
    string visitorId = 10; //访客ID
    string userId = 20; //商城用户ID
    int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
}