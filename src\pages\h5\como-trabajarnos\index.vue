<template>
  <div class="w-full bg-white">
    <div class="page-header px-[0.4rem] text-[#fff] overflow-auto pt-[0.6rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">Cómo trabajarnos</div>
    </div>
    <div class="pt-[1.2rem] pb-[1.6rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">¿Cómo Chilat le ayuda a importar de China?</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.36rem] mx-auto"></div>
      <div class="mt-[1rem] mb-[0.56rem] flex">
        <img :src="arrowRightRed" alt="como trabajarnos" class="w-[0.2rem] mr-[0.12rem]" loading="lazy" />
        <div class="text-[0.32rem] leading-[0.32rem] text-[#7F7F7F]">SI VIENE A CHINA</div>
      </div>
      <div class="rounded-[0.4rem] w-[335] h-[3.7688rem] overflow-hidden bg-white video-container">
        <transition name="video-transition" mode="out-in">
          <div :key="pageData.activeService?.id" class="w-full h-full">
            <video-you-tube
              :autoplay="pageData.isUserSwitched"
              :width="335"
              :height="188.44"
              :poster="pageData.activeService?.poster"
              :youtubeId="pageData.activeService?.id"
              :title="pageData.activeService?.title"
              :titleCh="pageData.activeService?.titleCh"
            ></video-you-tube>
          </div>
        </transition>
      </div>

      <n-space vertical :style="{ gap: '0.52rem 0' }" class="mt-[0.68rem]">
        <div
          v-for="item in serviceProcessChina"
          :key="item.id"
          class="flex items-center cursor-pointer"
          @click="onPlayVideo(item)"
        >
          <img
            loading="lazy"
            alt="como trabajarnos"
            class="mr-[0.16rem]"
            :src="item.id === pageData.activeService?.id ? videoing : video"
          />
          <div
            :class="item.id === pageData.activeService?.id ? 'font-medium !text-[#e50113]' : ''"
            class="text-[0.3rem] leading-[0.34rem]"
          >
            {{ item.title }}
          </div>
        </div>
      </n-space>

      <!-- <div class="mt-[1rem] mb-[0.56rem] flex justify-end">
        <img
          :src="arrowRightRed"
          alt="como trabajarnos"
          class="w-[0.2rem] mr-[0.12rem]"
          loading="lazy"
        />
        <div class="text-[0.4rem] leading-[0.4rem] text-[#7F7F7F]">
          SI NO VIENE A CHINA
        </div>
      </div>
      <div
        class="rounded-[0.4rem] w-[6.7rem] h-[3.7688rem] overflow-hidden bg-white"
      >
        <video-you-tube
          :autoplay="pageData.isUserSwitched"
          :width="335"
          :height="188.44"
          :key="pageData.activeServiceNoChina?.id"
          :poster="pageData.activeServiceNoChina.poster"
          :youtubeId="pageData.activeServiceNoChina?.id"
          :title="pageData.activeServiceNoChina?.title"
          :titleCh="pageData.activeServiceNoChina?.titleCh"
        ></video-you-tube>
      </div>
      <n-space vertical :style="{ gap: '0.52rem 0' }" class="mt-[0.68rem]">
        <div
          v-for="item in serviceProcessNoChina"
          :key="item.id"
          class="flex items-center cursor-pointer"
          @click="onPlayVideo(item)"
        >
          <img
            loading="lazy"
            alt="como trabajarnos"
            class="mr-[0.16rem]"
            :src="
              item.disabled
                ? videoDisabled
                : item.id === pageData.activeServiceNoChina?.id
                ? videoing
                : video
            "
          />
          <div
            :class="
              item.disabled
                ? 'text-[#7F7F7F] cursor-default'
                : item.id === pageData.activeServiceNoChina?.id
                ? 'font-medium !text-[#e50113]'
                : ''
            "
            class="text-[0.3rem] leading-[0.34rem] text-[#7F7F7F]"
          >
            {{ item.title }}
          </div>
        </div>
      </n-space> -->
    </div>
    <div
      class="rounded-[0.4rem] w-full h-[4.04rem] text-[#fff] pt-[0.5rem] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[0.48rem] leading-[0.56rem] font-medium">¿Por qué nuestros servicios pueden ayudarle?</div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="h-[1.28rem] px-[0.48rem] rounded-[10rem] mt-[0.56rem] contact-btn"
      >
        <div class="text-[0.36rem] leading-[0.36rem] mr-[0.16rem]">Leer más</div>
        <img
          alt="como trabajarnos"
          class="arrow-icon w-[0.88rem]"
          src="@/assets/icons/quienes-somos/arrow-line.svg"
          loading="lazy"
        />
      </n-button>
    </div>
    <MobileWhatsAppContact class="my-[0.48rem]" :showWhatsAppCtaBanner="false"></MobileWhatsAppContact>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import arrowRightRed from "@/assets/icons/como-trabajarnos/arrow-right-red.svg";
import servicePoster1 from "@/assets/icons/como-trabajarnos/servicePoster1.png";
import servicePoster2 from "@/assets/icons/como-trabajarnos/servicePoster2.png";
import servicePoster3 from "@/assets/icons/como-trabajarnos/servicePoster3.png";
import servicePoster4 from "@/assets/icons/como-trabajarnos/servicePoster4.png";
import servicePoster5 from "@/assets/icons/como-trabajarnos/servicePoster5.png";
import servicePoster6 from "@/assets/icons/como-trabajarnos/servicePoster6.png";
import redBg from "@/assets/icons/quienes-somos/mobile-red-bg.png";
import videoing from "@/assets/icons/como-trabajarnos/mobile-videoing.svg";
import video from "@/assets/icons/como-trabajarnos/mobile-video.svg";
// import videoDisabled from "@/assets/icons/como-trabajarnos/mobile-video-disabled.svg";

useHead({
  title: "Cómo trabajarnos - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/como-trabajarnos/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { text: "Nosotros" },
  { link: "/h5/como-trabajarnos", text: "Cómo trabajarnos" },
];

const serviceProcessChina = [
  {
    id: "b7X9D3BLvMc",
    poster: servicePoster1,
    title: "Emitir carta de invitación/reserva de hotel",
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: servicePoster2,
    title: "Recibir en el aeropuerto y visita al mercado",
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "GN6F6oLssUw",
    poster: servicePoster3,
    title: "Realizar pedidos y seguimiento de producción",
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "wWtOhkPDg5A",
    poster: servicePoster4,
    title: "Recibir los productos y hacer una inspección de calidad",
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: servicePoster5,
    title: "Cargar el contenedor y organizar los documentos de envío",
    titleCh: "5.装载集装箱并组织运输",
  },
];

const serviceProcessNoChina = [
  {
    id: "94ABcP_GQFs",
    poster: servicePoster6,
    title: "Obtención de productos (gratis) y confirmación de muestra",
    titleCh: "1.获得产品（免费）和样品确认",
  },
  {
    id: "VCgjAWzHB1A",
    disabled: true,
    poster: servicePoster2,
    title: "El mismo proceso si vienes a China",
    titleCh: "2.如果你来中国，同样的过程",
  },
];

const pageData = reactive(<any>{
  activeService: serviceProcessChina[0],
  activeServiceNoChina: serviceProcessNoChina[0],
  isUserSwitched: false,
});

function onPlayVideo(val: any) {
  if (val.disabled) return;

  // 检查是哪个列表的视频被点击
  const isServiceChina = serviceProcessChina.some((item) => item.id === val.id);

  if (isServiceChina) {
    pageData.activeService = val;
    pageData.isUserSwitched = true;
  }
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/como-trabajarnos/mobile-header-bg.png");
  background-repeat: no-repeat;
}

.video-transition-enter-active {
  transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.video-transition-leave-active {
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

.video-transition-enter-from {
  opacity: 0;
}

.video-transition-leave-to {
  opacity: 0;
  transform: scale(0.96);
}

.video-container {
  position: relative;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  will-change: opacity;
}

.contact-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

  .arrow-icon {
    transition: transform 0.8s cubic-bezier(0.25, 1, 0.5, 1), opacity 0.8s ease-in-out;
    opacity: 1;
  }

  &:hover {
    font-weight: 500;
    transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

    .arrow-icon {
      content: url("@/assets/icons/quienes-somos/arrow-red.svg");
      transform: scale(1.28);
    }
  }

  &:not(:hover) {
    transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);

    .arrow-icon {
      transform: scale(1);
      opacity: 1;
    }
  }
}
</style>
