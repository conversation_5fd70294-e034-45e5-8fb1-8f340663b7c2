syntax = "proto3";
package chilat.trade;

option java_package = "com.chilat.rpc.trade.param";

import "chilat/trade/purchase_common.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

message PurchasePageQueryParam {
  common.PageParam page = 1;
  PurchaseQueryParam query = 2;
}

message PurchaseQueryParam {
  int64 startCDate = 1; // 开始创建时间
  int64  endCDate = 2; // 结束创建时间
  string purchaseNo = 3; // 采购单号
  string purchaserId = 4; // 采购员
  string goodsNo = 5; // 商品编号
  string innerPurchaseNo = 6; // 内采单号
  PurchaseOrderState state = 7; // 状态
  string outOrderNo = 8; // 外部订单号
  string salesOrderNo = 9; //2.0订单号
  string salesManId = 10; // 业务员ID
}

message PurchaseSaveParam {
  string purchaseId = 1; // 采购单ID
  string subAccountId = 5; // 1688下单子账号ID
  PurchaseReceiverParam receiver = 2; // 收件人
  repeated PurchaseGoodsParam goods = 3; // 商品列表
  repeated string delGoodsIds = 4; // 删除的商品ID
  string salesOrderNo = 10; //2.0开单orderNo
  PurchaseSaveExtParam extParam = 20;
}

message PurchaseSaveExtParam {
  string userDefinedOrderNo = 1; //自定义订单编号
  string userDefinedCustomerNo = 2; //自定义客户编号
  string salesManId = 3; //业务员Id
  string salesManName = 4; //业务员名称
}

message PurchaseReceiverParam {
  string warehouseId = 1; // 仓库ID
  string purchaserId = 2; // 采购员ID
  string purchaserName = 3; // 采购员名称
  string remark = 4; // 备注
  string receiverName = 5; // 收件人
  string phone = 6; // 手机
  string postcode = 7; // 邮编
  string province = 8; // 省
  string city = 9; // 市
  string district = 10; // 区
  string town = 11; // 镇
  string streetAddress = 12; // 街道地址
  string buyerRemark = 13; // 下单备注
}

message PurchaseGoodsParam {
  string goodsId = 1; // 商品ID
  string sourceGoodsId = 2; // 1688商品ID
  string goodsName = 3; // 商品名称
  string goodsNameCn = 4; // 商品名称中文
  string imageUrl = 5; // 商品图片
  string sourceSkuId = 6; // SKU ID
  string sourceSpecId = 7; // SKU规格ID
  repeated commodity.GoodsExtendItem specList = 8; // 规格
  int32 count = 9; // 数量
  double price = 10; // 单价
  string sellerId = 11; // 卖家ID
  string extra = 12; // 额外信息
  string innerSkuId = 13; // 内部 SKU ID
  string innerGoodsId = 14; // 内部goodsId
  string innerOrderLineNo = 15; // 2.0订单行号
}

message PurchaseOrderPageQueryParam {
  common.PageParam page = 1;
  PurchaseOrderQueryParam query = 2;
}

message PurchaseOrderQueryParam {
  string innerPurchaseId = 1; // 内采单ID
  PurchasePayState payState = 2; // 支付状态
  PurchaseOrderState orderState = 3; // 订单状态
  PurchaseOrderState goodsState = 4; // 商品状态
  string orderNo = 5; // 订单号
  string goodsNo = 6; // 商品编号
}

message PurchaseOrderParam {
  string purchaseId = 1; // 采购单ID
  string innerPurchaseId = 2; // 内采单ID
  repeated string orderIds = 3; // 订单ID列表
  repeated string goodsIds = 4; // 商品ID列表
  bool isCopy = 5; // 是否复制
}