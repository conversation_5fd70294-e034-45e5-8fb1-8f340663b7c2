syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.param";

import "chilat/foundation/foundation_common.proto";
import "common.proto";

// 爬虫提交
message CrawlSubmitParam {
  string goodsTitle = 1; // 商品标题
  string goodsDesc = 2; // 商品描述
  string goodsDescText = 3; // 商品描述文本
  string goodsUrl = 4; // 商品链接
  string categoryId = 5; // 分类ID
  string categoryPath = 6; // 分类路径
  repeated CrawlGoodsMediaParam goodsImages = 7; // 商品图片
  repeated CrawlGoodsMediaParam goodsVideos = 8; // 商品视频
  repeated CrawlGoodsAttrParam goodsAttrs = 9; // 商品属性
  repeated CrawlGoodsSpecParam goodsSpecs = 10; // 商品规格
  CrawlGoodsPriceParam goodsPrice = 11; // 商品价格
  string sourceGoodsId = 12; // 源商品ID
  CrawlSourcePlatform sourcePlatform = 13; // 源平台ID
  bool forceSave = 14; // 强制保存
  string supplyName = 15; // 供应商名称
  string companyName = 16; // 供应商公司
  string rawData = 17; // 原始数据
  int32 minBuyQuantity = 18; //最小购买数量
  int32 minIncreaseQuantity = 19; //最小加购数量
  double goodsWeight = 20; // 商品重量
  double goodsLength = 21; // 商品长度
  double goodsWidth = 22; // 商品宽度
  double goodsHeight = 23; // 商品高度
  int32 packageInsideCount = 24; // 包装内件数
  string priceUnit = 25; //价格单位
  bool translated = 26; //是否翻译
  CrawlMethod crawlMethod = 27; // 抓取方式
  string status = 28; // 商品状态
  string goodsTitleCn = 101; // 商品标题中文
  string priceUnitCn = 102; // 价格单位中文
  int32 soldOut = 103; // 商品销量
  double tradeMedalLevel = 104; // 卖家交易勋章
  double compositeServiceScore = 105; // 综合服务分
  double logisticsExperienceScore = 106; // 物流体验分
  double disputeComplaintScore = 107; // 纠纷解决分
  double offerExperienceScore = 108; // 商品体验分
  double consultingExperienceScore = 109; // 咨询体验分
  double repeatPurchasePercent = 110; // 卖家回头率
  double afterSalesExperienceScore = 111; // 退换体验分
  string sellerOpenId = 120; // 卖家加密标识
  bool isOnline = 200; // 是否直接上架
  repeated string goodsTagIds = 201; // 商品打标
  bool updateTagWithAddMode = 202; //是否以“添加模式”更新商品标签（传true，不删除已有标签）
}

message CrawlGoodsMediaParam {
  string mediaCover = 1; // 封面
  string mediaUrl = 2; // 链接
}

message CrawlGoodsAttrParam {
  string attrName = 1; // 属性名称
  string attrValue = 2; // 属性值
  string attrNameCn = 101; // 属性名称中文
  string attrValueCn = 102; // 属性值中文
}

message CrawlGoodsSpecParam {
  string specId = 1; // 规格id
  string specName = 2; // 规格名称
  CrawlGoodsSpecValueParam singleValue = 3; // 规格值
  repeated CrawlGoodsSpecValueParam specValues = 4; // 规格值列表
  string specNameCn = 101; // 规格名称中文
}

message CrawlGoodsSpecValueParam {
  string id = 1; // 规格值id
  string name = 2; // 规格值
  string color = 3; // 规格值颜色
  string fileName = 4; // 规格值图片
  string nameCn = 101; // 规格值中文
}

message CrawlGoodsPriceParam {
  double rate = 1; // 汇率
  string currency = 2; // 币种
  int32 rule = 3; // 规则，1、固定价 2、阶梯价 3、规格价
  repeated CrawlGoodsPriceInfoParam prices = 4; // 价格信息（旧）
  repeated CrawlGoodsPriceInfoParam skus = 5; // sku价格信息（新）
}

message CrawlGoodsPriceInfoParam {
  string skuId = 1; // sku号
  double dollarPrice = 2; // 美元价格
  double price = 3; // 本地价格
  int32 min = 4; // 最小数量
  int32 max = 5; // 最大数量
  double dollarPriceLow = 6; // 美元最低价格
  double dollarPriceHigh = 7; // 美元最高价格
  double priceLow = 8; // 本地最低价格
  double priceHigh = 9; // 本地最高价格
  repeated CrawlGoodsSpecParam goodsSpecs = 10; // 对应规格组合（新）
  repeated string specs = 11; // 对应规格字符串（旧）
  repeated CrawlGoodsStepPriceParam stepPrices = 12; // 规格阶梯价
  int32 amountOnSale = 13; // 库存
  string specId = 14; // sku规格号
}

message CrawlGoodsStepPriceParam {
  double price = 1; // 本地价格
  int32 min = 2; // 最小数量
  int32 max = 3; // 最大数量
}

message CrawlGoodsPageQueryParam {
  common.PageParam page = 1;
  CrawlGoodsQueryParam query = 2;
}

message CrawlImagePageQueryParam {
  common.PageParam page = 1;
  CrawlImageQueryParam query = 2;
}

// 商品查询
message CrawlGoodsQueryParam {
  string keyword = 1; // 搜索关键字
  CrawlSourcePlatform sourcePlatform = 2; // 商品来源
  CrawlSupplyState supplyState = 3; // 货源状态
  CrawlSyncState syncState = 4; // 同步状态
  repeated string sourceGoodsIds = 5; // 商品ID
}

// 商品图搜
message CrawlImageQueryParam {
  string imageId = 1; // 图片ID
  string keyword = 2; // 关键字
  string categoryId = 3; // 分类ID
  string sort = 4; // 排序
  double priceStart = 5; // 最小价格
  double priceEnd = 6; // 最大价格
}

// 商品货源
message CrawlGoodsSupplyParam {
  string id = 1; // 货源ID
  string goodsId = 2; // 商品ID
  double supplyPrice = 3; // 供货价
  CrawlSubmitParam goods = 4; // 商品信息
}

// 商品货源查询
message CrawlGoodsSupplyQueryParam {
  repeated string goodsIds = 1; // 商品ID
  repeated string supplyIds = 2; // 货源标识
  CrawlSourcePlatform sourcePlatform = 3; // 货源平台
  GoodsSource goodsSource = 4; // 商品来源
}

// 商品货源删除
message CrawlGoodsSupplyRemoveParam {
  string id = 1; // 货源ID
  string supplyId = 2; // 货源标识
}

// 错误上报
message CrawlErrorReportParam {
  string errorMessage = 1; // 错误信息
  string scriptURI = 2; // 脚本URI
  int32 lineNumber = 3; // 行号
  int32 columnNumber = 4; // 列号
  string stack = 5; // 错误堆栈
  string version = 6; // 插件版本
}

// 抓取1688参数
message Crawl1688Param {
  repeated string categoryIds = 1; // 类目ID
  string offerId = 2; // 1688商品ID
  bool crawlShop = 3; // 是否抓取同店铺其他商品，默认false
  bool forceSave = 4; // 如已存在，是否强制保存
  repeated string goodsTagIds = 5; // 商品打标
  bool updateTagWithAddMode = 6; //是否以“添加模式”更新商品标签（传true，不删除已有标签）
}

// 发布商品
message CrawlPublishParam {
  string id = 1; // 商品ID
  bool isOnline = 2; // 是否上架
}