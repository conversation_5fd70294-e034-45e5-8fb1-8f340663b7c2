<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[24px] py-[50px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-medium mt-[40px]">Búsqueda de proveedores y productos</div>
    </div>
    <div class="pt-[150px] px-[38px] flex">
      <div class="w-[645px] h-[362.81px] rounded-[20px] overflow-hidden flex-shrink-0 mr-[32px]">
        <video-you-tube
          :width="645"
          :height="362.81"
          youtubeId="qyp6EorLxbQ"
          titleCh="我们的合作伙伴怎么说"
          :poster="sourcingPoster"
        ></video-you-tube>
      </div>
      <div class="mt-[26px]">
        <div class="text-[34px] leading-[40px] font-medium">
          ¿Cómo encontrar proveedores confiables y obtener precios competitivos de productos sin venir a China?
        </div>
        <div class="text-[18px] leading-[22px] text-[#7f7f7f] mt-[24px]">
          Como su socio de abastecimiento más confiable, le brindamos lo mejor de la fabricación de China.
        </div>
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[228px] rounded-[500px] mt-[54px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px]">Póngase en contacto</span>
        </n-button>
      </div>
    </div>
    <div class="pt-[150px] px-[38px] flex flex-col items-center">
      <div class="text-[34px] leading-[34px] font-medium font-medium text-center">
        Servicio de agente de abastecimiento de China único
      </div>
      <div class="w-[954px] mx-auto text-[18px] leading-[22px] text-[#7f7f7f] mt-[16px] text-center">
        Lo ayudamos a encontrar fábricas, obtener precios competitivos, hacer un seguimiento de la producción,
        garantizar la calidad y entregar los productos en la puerta.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="flex mx-auto justify-evenly mt-[110px]">
        <div v-for="(step, index) in productServiceInfo" :key="index" class="flex items-start">
          <div class="w-[198px] flex-shrink-0 flex flex-col items-center text-center">
            <img loading="lazy" :src="step.icon" :alt="vip" class="w-[136px] h-[136px] flex-shrink-0" />
            <div class="text-[20px] leading-[24px] mt-[34px]">
              {{ step.title }}
            </div>
            <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px]">
              {{ step.description }}
            </div>
          </div>
          <img
            loading="lazy"
            v-if="index < productServiceInfo.length - 1"
            :src="stepArrow"
            class="mt-[68px] mr-[10px]"
          />
        </div>
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="global-navigation-btn w-[228px] mx-auto rounded-[500px] mt-[94px] h-[50px]"
      >
        <span class="text-[18px] leading-[18px]">Trabajar con Chilat</span>
      </n-button>
    </div>
    <div class="pt-[150px] pb-[80px] px-[38px] flex flex-col items-center">
      <div class="text-[34px] leading-[34px] font-medium font-medium text-center">
        Valor de abastecimiento sostenible desde 2003
      </div>
      <div class="w-[954px] mx-auto text-[18px] leading-[22px] text-[#7f7f7f] mt-[16px] text-center">
        Hemos visto que las empresas pierden tiempo y dinero innecesariamente cuando intentan obtener productos y
        servicios sin asistencia profesional. En CHILAT, eliminamos este riesgo por completo. Este es nuestro valor
        agregado.
      </div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="w-full flex justify-between flex-wrap mt-[78px]">
        <div
          v-for="(item, index) in supplyValueInfo"
          :key="index"
          class="relative w-[392px] min-h-[428px] bg-white border border-[#333] rounded-[20px] text-center"
        >
          <img loading="lazy" :src="item.icon" :alt="vip" class="w-full" />
          <div class="text-[20px] leading-[24px] mt-[28px]">
            {{ item.title }}
          </div>
          <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[12px] px-[12px]">
            {{ item.description }}
          </div>
        </div>
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="global-navigation-btn w-[228px] mx-auto rounded-[500px] mt-[94px] h-[50px]"
      >
        <span class="text-[18px] leading-[18px]">Iniciar mi proyecto</span>
      </n-button>
    </div>
    <div class="w-full h-[854px] py-[70px] px-[36px] relative bg-[#F7F7F7] overflow-hidden">
      <div class="w-[1068px] h-[1068px] rounded-full bg-[#fff] absolute top-[-75px] right-[-62px]"></div>
      <div class="relative z-1 flex flex-col items-center">
        <div class="text-[34px] leading-[34px] font-medium font-medium text-center">Por qué elegirnos</div>
        <div class="w-[954px] mx-auto text-[18px] leading-[22px] text-[#7f7f7f] mt-[16px] text-center">
          No solo somos una empresa de abastecimiento de China, sino su socio a largo plazo. Adaptamos nuestros
          servicios para apoyar mejor a su negocio.
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
        <div class="flex mt-[78px]">
          <img
            loading="lazy"
            src="@/assets/icons/busqueda-de-proveedores-y-productos/team.png"
            alt="vip"
            class="w-[645px] mr-[34px] flex-shrink-0"
          />
          <div class="text-[16px] leading-[20px] py-[20px] flex flex-col justify-between">
            <div class="flex items-center">
              <img
                loading="lazy"
                src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
                alt="vip"
                class="w-[36px] mr-[12px]"
              />
              <div>
                Servicio personal de
                <span class="font-medium"> asistente de habla hispana</span>, comunicación
                <span class="font-medium">sin barreras</span>.
              </div>
            </div>
            <div class="flex items-center">
              <img
                loading="lazy"
                src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
                alt="vip"
                class="w-[36px] mr-[12px]"
              />
              <div>
                Nos enfocamos en servir a
                <span class="font-medium">clientes latinoamericanos</span>
                durante <span class="font-medium">22 años</span>, los conocemos mejor.
              </div>
            </div>
            <div class="flex items-center">
              <img
                loading="lazy"
                src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
                alt="vip"
                class="w-[36px] mr-[12px]"
              />
              <div>
                Como empresa
                <span class="font-medium">confiable de abastecimiento en China,</span>
                contamos con servicios de cadena de suministro sólidos y competitivos.
              </div>
            </div>
            <div class="flex items-center">
              <img
                loading="lazy"
                src="@/assets/icons/busqueda-de-proveedores-y-productos/advantage-icon.svg"
                alt="vip"
                class="w-[36px] mr-[12px]"
              />
              <div>
                Damos servicio
                <span class="font-medium">a empresas de todos los tamaños;</span>
                Desde pymes hasta grandes corporaciones y nuestro equipo puede atender exactamente lo que le falta a su
                negocio.
              </div>
            </div>
          </div>
        </div>
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          @click="onWhatsAppClick"
          class="global-navigation-btn w-[308px] mx-auto rounded-[500px] mt-[94px] h-[50px]"
        >
          <span class="text-[18px] leading-[18px]">Obtenga su consulta gratuita</span>
        </n-button>
      </div>
    </div>
    <div class="pt-[80px] pb-[150px]">
      <div class="text-[34px] leading-[34px] font-medium font-medium text-center">Que dicen nuestros socios</div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <div class="w-[774px] h-[435.375px] rounded-[20px] overflow-hidden mt-[78px] mx-auto">
        <video-you-tube
          :width="774"
          :height="435.375"
          youtubeId="D7RZTZdWX0o"
          titleCh="我们的合作伙伴怎么说"
          :poster="feedbackPoster"
        ></video-you-tube>
      </div>
    </div>
    <div
      class="rounded-[20px] w-full h-[304px] text-[#fff] pt-[50px] text-center"
      :style="{
        backgroundImage: `url(${redBg})`,
        backgroundSize: '100% 100%',
      }"
    >
      <div class="text-[34px] leading-[34px] font-bold">¿Ya tiene un proveedor?</div>
      <div class="text-[20px] leading-[20px] mt-[40px]">
        Deje que nuestro equipo de control de calidad proteja la seguridad de su importación
      </div>
      <n-button
        size="large"
        color="#fff"
        text-color="#e50113"
        @click="onWhatsAppClick"
        class="w-[262px] h-[78px] rounded-[500px] mt-[44px] global-contact-btn"
      >
        <span class="text-[18px] leading-[18px] mr-[8px]">Más información</span>
        <img loading="lazy" alt="vip" class="arrow-icon" src="@/assets/icons/quienes-somos/arrow-line.svg" />
      </n-button>
    </div>
    <div class="py-[150px] px-[38px]">
      <div class="text-[34px] leading-[34px] font-medium font-medium text-center">Preguntas más frecuentes</div>
      <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[36px]"></div>
      <n-collapse
        class="mt-[78px] w-[858px] mx-auto"
        :show-arrow="false"
        :on-update:expanded-names="onUpdateExpandedNames"
      >
        <n-collapse-item
          v-for="(question, index) in questionInfo"
          :key="index"
          :name="question.question"
          class="p-[20px]"
          :class="{
            'border-b-1 border-[#F2F2F2]': index === questionInfo.length - 1,
          }"
        >
          <template #header>
            <div
              class="flex"
              :class="{
                'text-[#e50113]': pageData.expandedQuestions.includes(question.question),
              }"
            >
              <div
                class="text-[14px] h-[22px] leading-[22px] text-[#7F7F7F] mr-[16px]"
                :class="{
                  'text-[#e50113]': pageData.expandedQuestions.includes(question.question),
                }"
              >
                0{{ index + 1 }}
              </div>
              <span class="text-[18px] leading-[22px]">{{ question.question }}</span>
            </div>
          </template>
          <template #header-extra="{ collapsed }">
            <img
              loading="lazy"
              v-if="collapsed"
              alt="vip"
              class="w-[22px] ml-[60px]"
              src="@/assets/icons/common/expand-red.svg"
            />
            <img
              loading="lazy"
              v-else
              alt="vip"
              class="w-[22px] ml-[60px]"
              src="@/assets/icons/common/collapse-red.svg"
            />
          </template>
          <template #arrow><span class="hidden"></span></template>
          <div
            v-html="question.answer"
            data-spm-box="article-inner-link"
            class="text-[16px] leading-[24px] text-[#333] ml-[50px] mr-[60px]"
          ></div>
        </n-collapse-item>
      </n-collapse>
    </div>
    <WhatsAppContact />
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import sourcingPoster from "@/assets/icons/busqueda-de-proveedores-y-productos/sourcing-poster.png";
import step1 from "@/assets/icons/busqueda-de-proveedores-y-productos/step1.png";
import step2 from "@/assets/icons/busqueda-de-proveedores-y-productos/step2.png";
import step3 from "@/assets/icons/busqueda-de-proveedores-y-productos/step3.png";
import step4 from "@/assets/icons/busqueda-de-proveedores-y-productos/step4.png";
import step5 from "@/assets/icons/busqueda-de-proveedores-y-productos/step5.png";
import stepArrow from "@/assets/icons/busqueda-de-proveedores-y-productos/step-arrow.svg";
import supplierIntegration from "@/assets/icons/busqueda-de-proveedores-y-productos/supplier-integration.png";
import paymentSecurity from "@/assets/icons/busqueda-de-proveedores-y-productos/payment-security.png";
import saveTime from "@/assets/icons/busqueda-de-proveedores-y-productos/save-time.png";
import feedbackPoster from "@/assets/icons/busqueda-de-proveedores-y-productos/feedback-poster.png";
import redBg from "@/assets/icons/quienes-somos/red-bg.png";

useHead({
  title: "VIP - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/vip/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/vip/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/servicios", text: "Servicios" },
  {
    link: "/busqueda-de-proveedores-y-productos",
    text: "Búsqueda de proveedores y productos",
  },
];

const productServiceInfo = [
  {
    icon: step1,
    title: "Cumpla con sus necesidades",
    description:
      "Simplemente háganos saber qué tipo de productos necesita, especificaciones y la cantidad que necesita.",
  },
  {
    icon: step2,
    title: "Proveedores de abastecimiento",
    description:
      "Nuestro experto en abastecimiento le ayudará a buscar proveedores en China y le ofrecerá el precio más competitivo de acuerdo con los requisitos de su producto.",
  },
  {
    icon: step3,
    title: "Muestreo y producción",
    description:
      "Una vez que apruebe la muestra que le enviamos, firmaremos un contrato chino con la fábrica y daremos seguimiento a la producción.",
  },
  {
    icon: step4,
    title: "Control de calidad",
    description:
      "Realizamos un control de calidad al recoger la mercancía en nuestro almacén y le proporcionamos informes de inspección antes del envío.",
  },
  {
    icon: step5,
    title: "Logística",
    description:
      "Brindamos servicio de entrega puerta a puerta a su solicitud, incorporando transportistas para recoger y entregar los productos.",
  },
];

const supplyValueInfo = [
  {
    icon: supplierIntegration,
    title: "Proveedor todo en uno",
    description:
      "Independientemente de los productos que necesite, lo ayudaremos a encontrar fábricas, obtener excelentes tarifas, garantizar una calidad superior y entregar los productos de manera segura.",
  },

  {
    icon: paymentSecurity,
    title: "Proteja la seguridad del pago",
    description:
      "Verificamos al proveedor antes de firmar contratos y realizamos pagos después de la inspección de los productos, lo protegemos de estafadores y solicitantes en línea que parecen estar en todas partes en línea.",
  },
  {
    icon: saveTime,
    title: "Ahorre tiempo y dinero",
    description:
      "Puede gastar mucho tiempo y dinero en proveedores no calificados y puede encontrar su proveedor ideal después de pasar mucho tiempo investigando. Con CHILAT le ayudaremos a gestionar sus proveedores en su nombre y ya no tendrá este tipo de problemas.",
  },
];

const questionInfo = [
  {
    question: "¿Qué tipo de productos abastece su empresa?",
    answer:
      "En términos generales, abastecemos principalmente bienes de consumo, como ropa, joyas, juguetes, productos electrónicos, muebles, utensilios de cocina, ferretería y herramientas, hogar y jardín, etc. Pero, de hecho, podríamos obtener cualquier producto que necesite, por ejemplo, acabamos de comprar una excavadora de segunda mano para nuestro cliente recientemente.",
  },
  {
    question: "Si ya tengo mis propios proveedores, ¿cómo puedo trabajar con Chilat?",
    answer:
      "Si tiene su propio proveedor, lo ayudaremos a verificar la fábrica e inspeccionar la calidad de los productos, también organizaremos el envío. Si los proveedores provienen de todo el país, podríamos consolidar la mercadería en nuestro almacén y entregarle un contenedor completo.",
  },
  {
    question: "¿Cuál es el costo de su servicio de abastecimiento?",
    answer:
      "Las dos primeras etapas de nuestros servicios son totalmente gratuitas. Solo cobramos una tarifa de servicio del 3 ~ 5% dependiendo del monto de su pedido para las etapas posteriores de los servicios. Eso significa que si no está satisfecho con la cotización, no tiene que pagar nada.",
  },
  {
    question: "¿Cómo obtiene Chilat precios más competitivos?",
    answer:
      "<div>En China, no todas las fábricas tienen presencia en línea. Trabajamos con miles de fabricantes que no están disponibles en sitios como Alibaba.com o 1688.com, y obtenemos precios competitivos y económicos de ellos.</div><div style='margin-top: 6px;'>También llevamos a cabo nuestra debida diligencia con todos los proveedores que obtenemos utilizando nuestro acceso a bases de datos del gobierno y otros terceros para asegurarnos de que estamos tratando con una fábrica (y no con una empresa comercial que dice ser una fábrica) y la fábrica es confiable y tiene un historial de fabricación.</div>",
  },
  {
    question: "No tengo experiencia en la importación de China, ¿cómo puedo elegir su servicio?",
    answer:
      "<div>Si tiene la intención de importar desde China, no importa si no tiene experiencia en la importación, nuestro asistente de compras le brindará servicios gratuitos de consultoría de importación y lo guiará a través de todo el proceso de importación.</div><div style='margin-top: 6px;'>Muchos clientes se han convertido en expertos en importación para principiantes en importación bajo nuestra guía.</div>",
  },
];

const pageData = reactive(<any>{
  expandedQuestions: [],
});

function onUpdateExpandedNames(ids: any) {
  pageData.expandedQuestions = ids;
}
</script>
<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/busqueda-de-proveedores-y-productos/header-bg.png");
  background-repeat: no-repeat;
}
:deep(.n-collapse .n-collapse-item) {
  margin: 0;
}
:deep(.n-collapse .n-collapse-item .n-collapse-item__header) {
  padding: 0;
}
:deep(.n-collapse .n-collapse-item:not(:first-child)) {
  border-top: 1px solid #f2f2f2;
}
</style>
