syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.common";

import "common.proto";

enum GoodsOnlineDays {
  GOODS_ONLINE_DAYS_UNKNOWN = 0;
  GOODS_ONLINE_DAYS_TODAY = 1; //近24小时
  GOODS_ONLINE_DAYS_SEVEN = 2; //近7天
  GOODS_ONLINE_DAYS_FOURTEEN = 3; //近14天
}

enum StockLocation {
  STOCK_LOCATION_UNKNOWN = 0;
  STOCK_LOCATION_CHINA = 1; //中国有库存
  STOCK_LOCATION_LOCAL = 2; //墨西哥本地有库存
}