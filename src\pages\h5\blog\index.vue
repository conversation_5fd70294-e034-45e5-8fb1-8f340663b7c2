<template>
  <div class="w-full bg-white">
    <!-- banner/面包屑 -->
    <div v-if="!!queryForm.publishDate">
      <!-- 面包屑导航 -->
      <div>
        <n-breadcrumb separator=">">
          <n-breadcrumb-item>
            <icon-card
              size="24"
              color="#7F7F7F"
              class="mr-[-0.08rem] cursor-pointer"
              name="ic:sharp-home"
              @click="onNavigateHome"
            >
            </icon-card>
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            <div class="text-[#7F7F7F] text-[0.28rem] font-400 leading-[0.28rem]">
              <a href="/blog"> Blog </a>
            </div>
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            <div class="text-[#333] text-[0.28rem] font-500 leading-[0.28rem]">
              <div v-if="pageData.articleList?.length > 0">
                <span>Etpublicado en </span>
                <span class="ml-[0.08rem]">{{
                  timeFormatByZone(Number(route.query.date), false, false, false) ?? "Por Lalala"
                }}</span>
              </div>
              <div v-else>Por Lalala</div>
            </div>
          </n-breadcrumb-item>
        </n-breadcrumb>
      </div>
    </div>
    <div v-else>
      <div class="page-header text-[#fff] overflow-auto pt-[0.6rem]">
        <div class="pl-[0.4rem]">
          <Breadcrumb :items="breadcrumbItems" />
          <div class="text-[1.04rem] leading-[1.16rem] font-500 pt-[0.6rem]">Blog</div>
        </div>
        <div class="mt-[0.8rem] px-[0.4rem]">
          <n-input
            round
            status="error"
            v-model:value="queryForm.keyword"
            :on-blur="onSearchKeyword"
            @keydown.enter="onSearchKeyword"
          >
            <template #suffix>
              <icon-card
                size="32"
                name="mynaui:search"
                class="add-btn-list"
                color="#E50113"
                @click="onSearchKeyword"
              ></icon-card>
            </template>
          </n-input>
        </div>
      </div>
      <div class="mt-[0.8rem] mx-[0.4rem]">
        <!-- 标签列表 -->
        <div class="category-container">
          <div class="category-items" :class="{ expanded: pageData.isExpanded }">
            <span class="category-items-inner">
              <span v-for="(category, index) in pageData.categoryList" :key="index" class="category-item">
                <n-button
                  round
                  :bordered="false"
                  :type="category?.isSelected ? 'primary' : 'tertiary'"
                  @click="onCategoryClick(category)"
                  class="text-[14px] leading-[14px] px-[12px] py-[6px] h-[26px]"
                  :class="[category?.isSelected ? 'font-500 bg-[#E50113]' : 'font-400 bg-[#F2F2F2]']"
                >
                  {{ category.name }}
                </n-button>
              </span>
            </span>
          </div>
          <div class="py-[0.16rem]">
            <icon-card
              size="24"
              color="black"
              :name="pageData.isExpanded ? 'iconoir:nav-arrow-down' : 'iconoir:nav-arrow-up'"
              @click="onToggleExpand"
            ></icon-card>
          </div>
        </div>
        <!-- 搜索 -->
        <div
          v-if="pageData.articleList?.length > 0"
          class="flex justify-between mt-[0.68rem] py-[0.12rem] border-b border-[#7F7F7F]"
        >
          <div class="text-[0.56rem] font-medium leading-[0.68rem]">Todos los artículos</div>
        </div>
      </div>
    </div>
    <!-- 文章列表 -->
    <div class="mx-[0.4rem]" v-if="pageData.articleList?.length > 0">
      <div v-for="(article, index) in pageData.articleList" :key="index">
        <div class="py-[0.4rem] border-b border-[#F2F2F2] article-item">
          <div class="flex flex-col">
            <div @click="() => onArticleDetail(article)">
              <div v-if="article?.logo">
                <img :src="article?.logo" class="min-w-[90vw] h-[3.34rem] rounded-[0.08rem]" loading="lazy"  alt="blog">
              </div>
              <div v-else>
                <n-skeleton height="3.34rem" class="rounded-[0.08rem]" />
              </div>
            </div>
            <!-- 文章标签 -->
            <div class="flex flex-wrap gap-1 mt-[0.2rem]">
              <div v-for="category in article.articleCategories" :key="category.id">
                <n-button
                  round
                  :type="category?.isSelected ? 'primary' : 'tertiary'"
                  @click="onCategoryClick(category)"
                  class="font-400 text-[0.24rem] leading-[0.24rem]"
                >
                  {{ category.name }}
                </n-button>
              </div>
            </div>
            <!-- 文章标题 -->
            <div
              @click="() => onArticleDetail(article)"
              class="w-full mt-[0.2rem] text-[0.48rem] text-[#333] font-400 leading-[0.56rem]"
            >
              <p class="line-clamp-2">
                {{ article.title }}
              </p>
            </div>
            <!-- 文章内容 -->
            <div
              @click="() => onArticleDetail(article)"
              class="w-full mt-[0.12rem] text-[0.32rem] font-400 leading-[0.48rem] text-[#7F7F7F]"
            >
              <p class="line-clamp-2">
                {{ article.introduce }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div v-if="route.query.date" class="flex justify-center mt-[1rem] mb-[2.4rem]">- No hay más artículos -</div>
    </div>
    <!-- 没有文章 -->
    <div v-else class="flex flex-col mt-[0.68rem] mb-[7.2rem] px-[0.4rem]">
      <div class="py-[0.12rem] text-[0.56rem] font-400 leading-[0.68rem]">No se encontró nada</div>
      <hr class="border-t border-[#7F7F7F]" />
      <div class="mt-[0.4rem]" v-if="!!route.query.date">
        <n-input-group>
          <n-input
            round
            status="error"
            v-model:value="queryForm.keyword"
            :on-blur="onSearchKeyword"
            @keydown.enter="onSearchKeyword"
          >
            <template #prefix>
              <icon-card size="28" name="mynaui:search" color="#E50113"></icon-card>
            </template>
          </n-input>
          <n-button round color="#E50113" @click="onSearchKeyword">
            <icon-card size="24" name="mynaui:search" color="#fff"></icon-card>
            <span class="ml-1 text[0.36rem]">Buscar</span>
          </n-button>
        </n-input-group>
      </div>
      <div class="flex flex-wrap gap-1 text-[#7F7F7F] text-[0.28rem] font-400 leading-[0.48rem] mt-[0.4rem]">
        Parece que no podemos encontrar lo que estas buscando.Tal vez, continuar buscando pueda ayudar.
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="pageData.loading" class="flex justify-center my-[0.64rem]">
      <n-flex>
        <icon-card size="24" name="eos-icons:bubble-loading" color="#333"></icon-card>
        <div class="text-[#7F7F7F] text-[0.48rem] font-400 leading-[0.32rem] my-[0.08rem]">Cargando...</div>
      </n-flex>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import homeIcon from "@/assets/icons/common/home.svg";

const route = useRoute();

useHead({
  title: "Blog de abastecimiento mayorista en China-chilat News - Chilat",
  meta: [
    {
      name: "description",
      content:
        "Proporcionamos conocimientos sobre la compra al por mayor de productos en China y resolvemos consultas relacionadas con la importación, el transporte y los precios.",
    },
  ],
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/blog/`,
    },
  ],
});

const breadcrumbItems: any = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/blog", text: "Blog" },
];

const pageData = reactive<any>({
  loading: false,
  pageCount: 0,
  pageItems: 0,
  isExpanded: true,
  cateIndex: 0,
  articleList: [], // 文章列表
  categoryList: [{ id: undefined, name: "Todos los artículos", isSelected: true }],
});

const queryForm = reactive<any>({
  keyword: null,
  publishDate: null,
  cateId: null,
  pageSize: 20,
  pageNum: 1,
});

onMounted(() => {
  queryForm.cateId = route.query.cateId ?? undefined;
  queryForm.publishDate = route.query.date ?? undefined;
  // 获取文章分类列表
  onListArticleCategory();
  // 获取文章列表
  onPageList();
  // 监听滚动事件
  window.addEventListener("scroll", onHandleScroll);
});

onBeforeUnmount(() => {
  // 移除滚动事件监听
  window.removeEventListener("scroll", onHandleScroll);
});

// 监听滚动事件
const onHandleScroll = () => {
  const { scrollTop, clientHeight, scrollHeight } = document.documentElement;
  // console.log("==>>TODO 3370:", scrollTop, clientHeight, scrollHeight);
  console.log("==>>TODO 3371:", scrollTop + clientHeight >= scrollHeight - 900, pageData.loading, queryForm.pageNum);
  if (scrollTop + clientHeight >= scrollHeight - 900 && !pageData.loading && queryForm.pageNum < pageData.pageCount) {
    queryForm.pageNum += 1;
    onPageList();
  }
};

// 获取文章分类列表
async function onListArticleCategory() {
  const res: any = await useListArticleCategory({
    names: ["Chilat"],
  });
  if (res?.result?.code === 200) {
    for (let i = 0; i < res.data.length; i++) {
      const item = res.data[i];
      for (let j = 0; j < item?.children.length; j++) {
        const child = item.children[j];
        child["isSelected"] = route.query.cateId === child.id ? true : false;
        pageData.categoryList.push(child);
      }
    }
  }
}

async function onPageList() {
  pageData.loading = true;
  if (queryForm.pageNum > 1) {
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  const publishDate = !!queryForm.publishDate
    ? timeFormatByZone(Number(queryForm.publishDate), false, false, false)?.trim()
    : null;

  const res: any = await useSearchArticleList({
    page: {
      current: queryForm.pageNum,
      size: queryForm.pageSize,
    },
    keyword: queryForm.keyword,
    cateId: queryForm.cateId ?? null,
    publishDate: publishDate ?? null,
  });
  if (queryForm.pageNum === 1) {
    pageData.articleList = [];
  }
  console.log("==>>TODO 1122:", queryForm.pageNum);
  if (res?.result?.code === 200) {
    pageData.pageCount = res.page.pages;
    pageData.pageItems = res.page.total;

    pageData.articleList = [...pageData.articleList, ...res.data];
    pageData.articleList = pageData.articleList?.map((articleItem: any) => {
      articleItem.articleCategories = articleItem.articleCategories?.map((categoryItem: any) => {
        if (categoryItem.id === queryForm.cateId) {
          categoryItem["isSelected"] = true;
        } else {
          categoryItem["isSelected"] = false;
        }
        return categoryItem;
      });
      return articleItem;
    });
  }
  pageData.loading = false;
}

// 搜索关键字
async function onSearchKeyword() {
  queryForm.pageNum = 1;
  queryForm.keyword = queryForm.keyword?.trim();
  console.log("===>>TODO 2351:", queryForm.pageNum);
  await onPageList();
}

// 点击首页
function onNavigateHome() {
  navigateToPage("/", {}, false);
}

function onToggleExpand() {
  pageData.isExpanded = !pageData.isExpanded;
}

async function onCategoryClick(category: any) {
  queryForm.pageNum = 1;
  queryForm.cateId = category.id ?? null;
  pageData.categoryList = pageData.categoryList?.map((item: any) => {
    if (item.id === category.id) {
      item["isSelected"] = true;
    } else {
      item["isSelected"] = false;
    }
    return item;
  });

  await onPageList();
}

function onArticleDetail(article: any) {
  // TODO 待完善
  return navigateTo({
    path: `/h5/blog/${article.id}`,
    // 可以传递状态实现共享元素过渡
    state: { transitionName: "page-slide" },
  });
}
</script>

<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/blog/mobile-header-bg.png");
  background-repeat: no-repeat;
}

.category-container {
  display: flex;
  align-items: flex-start;
  max-width: 23.12rem;
  border-radius: 0.08rem;
}

.category-items {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 0.48rem;
  justify-content: center;
}

.category-items.expanded {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
}

.category-items-inner {
  display: inline;
}

.category-item {
  display: inline-block;
  margin-right: 0.12rem;
  margin-top: 0.12rem;
  font-size: 0.28rem;
}

.article-item {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.article-item:hover {
  transform: scale(1.05);
}
</style>
