<template>
  <div class="w-[1280px] mx-auto bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[24px] pt-[50px]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[80px] leading-[80px] font-medium mt-[40px]">Tiendas panorámicas en 3D</div>
      <div class="text-[18px] leading-[22px] mt-[118px] text-center">Miles de productos presentados online.</div>
    </div>
    <div class="pb-[200px] px-[62px]">
      <!-- 分类导航区域 -->
      <div class="category-container mt-[60px]" v-if="pageData.categoryList.length > 0">
        <div class="category-items" :class="{ expanded: pageData.isExpanded }">
          <div class="block">
            <span
              v-for="(category, index) in pageData.categoryList"
              :key="index"
              class="inline-block mr-[8px] mb-[8px]"
            >
              <div
                :class="
                  category === pageData.selectedCategory
                    ? ' bg-[#e50113] text-[#fff]'
                    : 'font-normal bg-[#F2F2F2] text-[#333333]'
                "
                @click="onCategoryClick(category)"
                class="px-[14px] py-[6px] text-[14px] leading-[14px] rounded-[500px] cursor-pointer transition-all duration-200"
              >
                {{ category }}
              </div>
            </span>
          </div>
        </div>
        <!-- 展开/收起按钮放在右侧 -->
        <div class="toggle-btn-wrapper cursor-pointer" @click="onToggleExpand">
          <img
            loading="lazy"
            alt="tiendas panoramicas en 3d"
            src="@/assets/icons/common/arrow-down.svg"
            class="transition-all duration-300 ml-[8px]"
            :class="{ 'rotate-180': pageData.isExpanded }"
          />
        </div>
      </div>

      <div>
        <div class="flex flex-wrap gap-x-[100px] gap-y-[42px] mt-[56px] min-h-[30vh]">
          <div v-for="item in pageData.storeList" :key="item.id">
            <a :href="`/tienda/${item.title}`" class="block group w-[214px]">
              <div class="relative w-[214px] h-[300px] rounded-[12px] overflow-hidden">
                <img
                  loading="lazy"
                  :src="item.cover"
                  alt="tiendas panoramicas en 3d"
                  class="w-full h-full object-cover object-center"
                />
                <!-- 蒙层 -->
                <div
                  v-if="item.product"
                  class="absolute inset-0 bg-black bg-opacity-45 flex flex-col justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-300 px-[10px] text-[#fff] pb-[48px]"
                >
                  <span class="text-[14px] leading-[26px] break-all line-clamp-6">
                    {{ item.product }}
                  </span>
                  <div class="h-[2px] w-full bg-white mt-[22px] mb-[8px]"></div>
                  <span class="text-[14px] leading-[14px] font-medium"> Producto principal </span>
                </div>
              </div>
              <div class="w-[214px] text-[18px] leading-[18px] mt-[18px] break-all line-clamp-1">
                {{ item.title }}
              </div>
            </a>
          </div>
        </div>
        <div class="flex justify-center mt-[90px]">
          <n-pagination
            show-size-picker
            show-quick-jumper
            :page-sizes="[20, 50, 100]"
            :page-count="pageData.pageCount"
            v-model:page="pageData.pageNum"
            v-model:page-size="pageData.pageSize"
            :on-update:page="onUpdatePageNum"
            :on-update:page-size="onUpdatePageSize"
            class="mt-3 text-center flex justify-center"
          >
            <template #prefix>
              <span class="px-2">Total:</span>
              <span>{{ pageData.pageItems }}</span>
            </template>
            <template #goto>Ir a</template>
          </n-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";
import { useRoute } from "vue-router";

const route = useRoute();
useHead({
  title: "Tiendas panorámicas en 3D - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/tiendas-panoramicas-en-3d/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/tiendas-panoramicas-en-3d/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/tiendas-panoramicas-en-3d", text: "Tiendas panorámicas en 3D" },
];

// 定义商店项目的接口类型
interface StoreItem {
  id: string | number;
  title: string;
  cover: string;
  product: string;
  // 其他可能存在的属性
}

const pageData = reactive({
  categoryList: [],
  selectedCategory: "",
  isExpanded: false, // 默认收起状态
  storeList: [] as StoreItem[],
  pageCount: 1,
  pageItems: 1,
  pageNum: 1,
  pageSize: 20,
});

await onListWordPressCategory();
onSearchWordPressList();

async function onListWordPressCategory() {
  const res: any = await useListWordPressCategory({});
  if (res?.result?.code === 200) {
    // 转换接口返回的数据格式
    res.data.categoryList.unshift("Todos");
    pageData.categoryList = res.data.categoryList;
    // 从URL获取选中的分类
    if (route.query.category) {
      pageData.selectedCategory = decodeURIComponent(route.query.category as string);

      // 获取选中分类的索引
      const selectedIndex = pageData.categoryList.findIndex((category) => category === pageData.selectedCategory);

      // 如果选中的分类索引大于等于5，则自动展开
      if (selectedIndex >= 6) {
        pageData.isExpanded = true;
      }
    } else {
      // 默认选中第一个分类
      pageData.selectedCategory = pageData.categoryList[0];
    }
  }
}

async function onSearchWordPressList() {
  const res: any = await useSearchWordPressList({
    category: pageData.selectedCategory === "Todos" ? "" : pageData.selectedCategory,
    page: {
      current: pageData.pageNum,
      size: pageData.pageSize,
    },
  });
  if (res?.result?.code === 200) {
    pageData.storeList = res.data;
    pageData.pageCount = res.page.pages;
    pageData.pageItems = res.page.total;
  }
}

function onToggleExpand() {
  pageData.isExpanded = !pageData.isExpanded;
}

function onCategoryClick(category: any) {
  pageData.selectedCategory = category;
  pageData.pageNum = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("category", encodeURIComponent(category));
  window.history.replaceState(null, "", url.toString());
  onSearchWordPressList();
}

async function onUpdatePageNum(page: number) {
  pageData.pageNum = page;
  onSearchWordPressList();
  // 平滑滚动到顶部
  window.scrollTo({
    top: 0,
  });
}

async function onUpdatePageSize(pageSize: number) {
  pageData.pageNum = 1;
  pageData.pageSize = pageSize;
  onSearchWordPressList();
  // 平滑滚动到顶部
  window.scrollTo({
    top: 0,
  });
}
</script>
<style scoped lang="scss">
.page-header {
  width: calc(100% - 76px);
  height: 367px;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/tiendas-panoramicas-en-3d/header-bg.png");
  background-repeat: no-repeat;
}

.category-container {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
  border-radius: 4px;
  position: relative;
  padding-right: 40px;
  overflow: hidden;
}

.category-items {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: unset;
  line-height: 24px;
  position: relative;
  max-height: 40px;
  transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.category-items.expanded {
  white-space: normal;
  overflow: visible;
  max-height: 500px;
}

.toggle-btn-wrapper {
  position: absolute;
  right: 0;
  top: 0;
  width: 68px;
  height: 30px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, #fff 60%, #fff 100%);
  z-index: 2;
}
</style>
