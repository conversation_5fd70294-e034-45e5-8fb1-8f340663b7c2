<template>
  <div class="w-[1280px] mx-auto bg-white pt-[50px] pb-[200px] flex gap-[80px] px-[62px]">
    <div class="w-[482px]">
      <div class="breadcrumb text-[14px] leading-[14px]">
        <div class="breadcrumb-item">
          <a href="/">
            <img loading="lazy" src="@/assets/icons/common/home-black.svg" alt="home" class="home-icon h-[14px]" />
          </a>
        </div>
        <img loading="lazy" src="@/assets/icons/common/arrow-right.svg" alt="arrow" class="arrow-icon" />
        <div class="breadcrumb-item">
          <a href="/tiendas-panoramicas-en-3d">
            <span class="breadcrumb-link">Tiendas panorámicas en 3D</span>
          </a>
        </div>
        <img loading="lazy" src="@/assets/icons/common/arrow-right.svg" alt="arrow" class="arrow-icon" />
        <!-- 当前标题 -->
        <div class="breadcrumb-item">
          <a :href="`/tienda/${pageData.storeDetail.title}`">
            <span class="breadcrumb-link active">
              {{ pageData.storeDetail.title }}
            </span>
          </a>
        </div>
      </div>
      <div class="mt-[80px]">
        <div class="text-[80px] leading-[80px] font-medium">
          {{ pageData.storeDetail.title }}
        </div>
        <div v-if="pageData.storeDetail.title" class="mt-[14px] text-[18px] leading-[30px]">
          <span class="font-medium mr-[6px]">Número de Local:</span>
          <span>{{ pageData.storeDetail.title }}</span>
        </div>
        <div v-if="pageData.storeDetail.product" class="mt-[4px] text-[18px] leading-[30px]">
          <span class="font-medium mr-[6px]">Producto Principal: </span>
          <span>{{ pageData.storeDetail.product }}</span>
        </div>
        <div v-if="pageData.storeDetail.pubDate" class="mt-[4px] text-[18px] leading-[30px]">
          <span class="font-medium mr-[6px]">Fecha de publicación: </span>
          <span>{{ pageData.storeDetail.pubDate }}</span>
        </div>
      </div>
    </div>
    <div class="w-[594px] h-[400px]">
      <iframe
        style="width: 100%; height: 100%"
        :src="pageData.storeDetail.link"
        allowfullscreen="allowfullscreen"
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

const route = useRoute();
const pageData = reactive<any>({
  storeDetail: null,
});

useHead({
  title: "Mercados de joyería de Guangzhou - Chila",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/tienda/${route.params.id}/`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/tienda/${route.params.id}/`,
    },
  ],
});

await onPageData();
async function onPageData() {
  const res: any = await useWordPressDetail({
    title: route.params.id,
  });
  if (res?.result?.code === 200) {
    pageData.storeDetail = res.data;
  }
}
</script>

<style scoped lang="scss">
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.home-icon:hover {
  content: url("@/assets/icons/common/home-red.svg");
}

.breadcrumb-link {
  color: #7f7f7f;
  transition: all 0.3s;
  cursor: pointer;
}

.breadcrumb-link:hover,
.breadcrumb-link.active {
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 6px;
  margin: 0 8px;
  filter: brightness(0) saturate(100%) invert(43%) sepia(0%) saturate(0%) hue-rotate(193deg) brightness(96%)
    contrast(90%);
}
</style>
