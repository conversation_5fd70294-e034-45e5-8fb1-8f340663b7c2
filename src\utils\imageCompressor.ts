interface CompressOptions {
  maxSizeKB?: number;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export class ImageCompressor {
  private static readonly DEFAULT_MAX_SIZE = 300; // 300KB
  private static readonly DEFAULT_QUALITY = 0.8;
  private static readonly DEFAULT_MAX_WIDTH = 1920;
  private static readonly DEFAULT_MAX_HEIGHT = 1080;

  /**
   * 压缩图片文件
   * @param file 输入的图片文件
   * @param options 压缩选项
   * @returns Promise<File> 压缩后的图片文件
   */
  static async compress(
    file: File,
    options: CompressOptions = {}
  ): Promise<File> {
    const {
      maxSizeKB = this.DEFAULT_MAX_SIZE,
      quality = this.DEFAULT_QUALITY,
      maxWidth = this.DEFAULT_MAX_WIDTH,
      maxHeight = this.DEFAULT_MAX_HEIGHT,
    } = options;

    // 如果文件小于最大尺寸，直接返回
    if (file.size / 1024 <= maxSizeKB) {
      return file;
    }

    // 创建图片对象
    const img = await this.createImage(file);
    if (!img) {
      return file; // 如果图片加载失败，返回原始文件
    }

    // 计算压缩后的尺寸
    const { width, height } = this.calculateDimensions(
      img.width,
      img.height,
      maxWidth,
      maxHeight
    );

    // 创建canvas并绘制图片
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return file; // 如果无法获取canvas上下文，返回原始文件
    }
    ctx.drawImage(img, 0, 0, width, height);

    // 压缩图片
    const outputType = file.type.startsWith('image/') ? file.type : 'image/jpeg';
    let blob = await this.canvasToBlob(canvas, outputType, quality);
    if (!blob) {
      return file; // 如果转换失败，返回原始文件
    }

    // 如果压缩后仍然超过最大尺寸，继续压缩
    if (blob.size / 1024 > maxSizeKB) {
      let low = 0.1;
      let high = quality;
      while (high - low > 0.05) {
        const mid = (low + high) / 2;
        blob = await this.canvasToBlob(canvas, outputType, mid);
        if (!blob) {
          return file; // 如果转换失败，返回原始文件
        }
        if (blob.size / 1024 > maxSizeKB) {
          high = mid;
        } else {
          low = mid;
        }
      }
    }

    // 创建新的File对象
    return new File([blob], file.name, {
      type: outputType,
      lastModified: Date.now(),
    });
  }

  /**
   * 创建Image对象
   */
  private static createImage(file: File): Promise<HTMLImageElement | null> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = () => resolve(null);
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Canvas转Blob
   */
  private static canvasToBlob(
    canvas: HTMLCanvasElement,
    type: string,
    quality: number
  ): Promise<Blob | null> {
    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => {
          resolve(blob || null);
        },
        type,
        quality
      );
    });
  }

  /**
   * 计算压缩后的尺寸，保持宽高比
   */
  private static calculateDimensions(
    width: number,
    height: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    if (width <= 0 || height <= 0) {
      return { width: 0, height: 0 }; // 返回默认值
    }

    let newWidth = width;
    let newHeight = height;

    if (width > maxWidth) {
      newWidth = maxWidth;
      newHeight = (height * maxWidth) / width;
    }

    if (newHeight > maxHeight) {
      newHeight = maxHeight;
      newWidth = (width * maxHeight) / height;
    }

    return { width: Math.floor(newWidth), height: Math.floor(newHeight) };
  }
}