syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";

message InquiryParam {
  string token = 10; //前端无需传该值
  repeated InquirySkuParam params = 20; //参数
  int32 siteId = 30; // 当前站点ID（即页面选中的配送国家ID）
}

message InquirySkuParam {
  string skuId = 1; //SKU ID
  int32 quantity = 2; //数量
  string spm = 3; //SPM跟踪码（在商详页中的SKU列表中直接询盘，从location.href中取spm参数，参考代码：window.MyStat.getLocationParam('spm')）
  string routeId = 40; //线路ID
}

message SubmitSkuParam {
  string token = 1; //前端无需传该值
  string countryId = 2; //国家id
  string submitName = 3; //姓名
  string email = 4; //邮箱
  string whatsapp = 5; //whatsapp
  string remark = 6; //备注
  bool isTemporary = 7; //是否暂存
  string areaCode = 8; //区号
  repeated InquirySkuParam params = 9; //参数
  bool fromCart = 10; //是否来自购物车
  string visitorId = 11; //询盘云访客id
  string postcode = 12; //邮编
  string address = 13; //地址
  int32 siteId = 200; // 当前站点ID（即页面选中的配送国家ID）
}

message GoodsLookingPageListParam {
  common.PageParam page = 1;
}