<template>
  <div class="w-[740px] mx-auto min-h-[50vh] pt-[50px] pb-[200px] break-words relative">
    <div class="breadcrumb text-[14px] leading-[14px]" v-if="pageData.articleDetail.articleCode">
      <div class="breadcrumb-item">
        <a href="/">
          <img loading="lazy" src="@/assets/icons/common/home-black.svg" alt="" class="home-icon h-[14px]" />
        </a>
      </div>
      <img loading="lazy" src="@/assets/icons/common/arrow-right.svg" alt="" class="arrow-icon" />
      <!-- 当前标题 -->
      <div class="breadcrumb-item">
        <a :href="`/article/${pageData.articleDetail.articleCode}`">
          <span class="breadcrumb-link active">
            {{ pageData.articleDetail.title }}
          </span>
        </a>
      </div>
    </div>
    <div class="w-[740px] flex mx-auto mt-[60px]">
      <div
        data-spm-box="article-inner-link"
        v-if="pageData.articleDetail?.content"
        v-html="pageData.articleDetail?.content"
        class="article-page whitespace-pre-wrap"
      ></div>
    </div>
    <div v-show="pageData.showLoading" class="loading-overlay">
      <n-spin stroke="#e50113" :show="pageData.showLoading" size="large"> </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const pageData = reactive<any>({
  articleDetail: "",
  showLoading: true,
});

await onGetArticle();
useHead({
  title: `${pageData.articleDetail.articleCode} - Chilat`,
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/article/${pageData.articleDetail.articleCode}`,
    },
    {
      rel: "alternate",
      media: "only screen and (max-width: 640px)",
      href: `https://www.chilat.com/h5/article/${pageData.articleDetail.articleCode}`,
    },
  ],
});
async function onGetArticle() {
  pageData.showLoading = true;
  const res: any = await useArticleDetail({
    id: route.params.id,
    articleCode: route.params.id,
  });
  pageData.showLoading = false;
  if (res?.result?.code === 200) {
    pageData.articleDetail = res?.data;
  }
}
</script>
<style scoped lang="scss">
:deep(strong) {
  font-weight: 500 !important;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.home-icon:hover {
  content: url("@/assets/icons/common/home-red.svg");
}

.breadcrumb-link {
  color: #333;
  transition: all 0.3s;
  cursor: pointer;
}

.breadcrumb-link:hover,
.breadcrumb-link.active {
  color: #333;
  font-weight: 500;
}

.arrow-icon {
  width: 6px;
  margin: 0 8px;
  filter: brightness(0) saturate(100%) invert(43%) sepia(0%) saturate(0%) hue-rotate(193deg) brightness(96%)
    contrast(90%);
}
.loading-overlay {
  position: absolute;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
</style>
