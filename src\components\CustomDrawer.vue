<template>
  <div class="custom-drawer-container">
    <!-- 遮罩层 -->
    <transition name="fade">
      <div class="drawer-mask" v-show="show" @click="handleClose"></div>
    </transition>

    <!-- 抽屉内容 -->
    <transition name="slide">
      <div class="custom-drawer" v-show="show">
        <!-- 自定义顶部 -->
        <slot name="header"></slot>

        <!-- 主要内容 -->
        <div class="drawer-content">
          <slot></slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  headerHeight: {
    type: String,
    default: "1.28rem",
  },
});

// 定义事件
const emit = defineEmits(["update:show", "close"]);

// 关闭抽屉
const handleClose = () => {
  emit("update:show", false);
  emit("close");
};

// 监听显示状态，控制页面滚动
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }
);
</script>

<style scoped>
/* 抽屉容器 */
.custom-drawer-container {
  position: absolute;
  top: v-bind("props.headerHeight");
  left: 0;
  width: 100%;
  height: calc(100vh - v-bind("props.headerHeight"));
  z-index: 998;
  pointer-events: none;
}

/* 遮罩层 */
.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  pointer-events: auto;
}

/* 抽屉内容 */
.custom-drawer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-color: #fff;
  z-index: 1000;
  overflow-y: auto;
  pointer-events: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform-origin: top center;
}

/* 抽屉内容容器 */
.drawer-content {
  padding: 0;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
  transform: scaleY(-100%);
}
</style>
