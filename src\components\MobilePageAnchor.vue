<template>
  <div class="fixed-bottom" @click="pageData.dialogVisible = true">
    <img
      loading="lazy"
      src="@/assets/icons/common/list.svg"
      alt="Índice"
      class="w-[0.4rem]"
    />
    <span class="text-[0.36rem] leading-[0.36rem]">Índice</span>
  </div>
  <n-drawer
    :resizable="true"
    default-width="100%"
    :height="props.anchorHeight"
    placement="bottom"
    v-model:show="pageData.dialogVisible"
    title="Índice"
    closable
    :auto-focus="false"
  >
    <div class="relative pt-[0.28rem] pb-[0.48rem]">
      <div class="text-[0.36rem] leading-[0.36rem] text-center">Índice</div>
      <icon-card
        color="#222"
        name="iconamoon:close-light"
        @click="pageData.dialogVisible = false"
        class="w-[0.48rem] absolute right-[0.26rem] top-[0.26rem]"
      ></icon-card>
    </div>
    <div class="px-[0.16rem] flex flex-col" ref="anchorContainerRef">
      <a
        class="anchor-item"
        v-for="item in anchorItems"
        :key="item.title"
        :title="item.title"
        :href="item.href"
        :class="{
          'active-anchor-item': item.href === pageData.currentHash,
        }"
        @click="pageData.dialogVisible = false"
      >
        {{ item.title }}
      </a>
    </div>
  </n-drawer>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
const route = useRoute();

// 定义锚点项接口
interface AnchorItem {
  title: string;
  href: string;
}

const props = defineProps({
  anchorItems: {
    type: Array as PropType<AnchorItem[]>,
    default: () => [],
  },
  anchorHeight: {
    type: String,
    default: "6rem",
  },
});

const pageData = reactive({
  dialogVisible: false,
  currentHash: "",
});

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = window.setTimeout(() => fn(...args), delay);
  };
};

/**
 * 检测页面滚动，确定当前活跃锚点并更新路由
 */
const detectScrollPosition = debounce(() => {
  // 收集所有锚点元素
  const sections = [];

  for (const item of props.anchorItems) {
    const id = item.href.replace("#", "");
    const element = document.getElementById(id);

    if (element) {
      const rect = element.getBoundingClientRect();
      sections.push({
        id,
        element,
        top: rect.top,
        href: item.href,
      });
    }
  }

  if (sections.length === 0) return;

  // 按照从上到下的顺序排序
  sections.sort((a, b) => a.top - b.top);

  // 找到当前视口中最上方的锚点
  let activeSection = sections[0]; // 默认是第一个
  const scrollTop = window.scrollY;
  const viewportHeight = window.innerHeight;
  const viewportThreshold = scrollTop + viewportHeight * 0.25; // 使用视口的上四分之一作为阈值

  for (const section of sections) {
    const elemTop = section.element.getBoundingClientRect().top + scrollTop;
    // 如果锚点在阈值内或上方，则更新为最接近的锚点
    if (elemTop <= viewportThreshold) {
      activeSection = section;
    } else {
      break;
    }
  }

  // 如果找到活跃锚点，且与当前hash不同，则更新路由
  if (activeSection && activeSection.href !== pageData.currentHash) {
    pageData.currentHash = activeSection.href;

    // 使用replaceState而不是router.push，避免创建新的历史记录
    window.history.replaceState(
      null,
      "",
      window.location.pathname + window.location.search + activeSection.href
    );
  }
}, 100);

onMounted(() => {
  pageData.currentHash = route.hash;
  window.addEventListener("scroll", detectScrollPosition, { passive: true });
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", detectScrollPosition);
});
</script>

<style scoped lang="scss">
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1rem;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  box-shadow: 0 -0.04rem 0.2rem rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.anchor-item {
  padding: 0.28rem 0.4rem;
  font-size: 0.32rem;
  line-height: 0.384rem;
  border-left: 0.06rem solid #f2f2f2;
}
.active-anchor-item {
  border-left: 0.06rem solid #e50113;
  background-color: #f2f2f2;
}
</style>
