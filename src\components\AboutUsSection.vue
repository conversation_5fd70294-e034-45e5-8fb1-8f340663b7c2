<template>
  <div class="w-full pt-[16px] border-t-1 border-[#7F7F7F] mt-[120px]">
    <div class="text-[28px] leading-[28px] font-medium">Descubre más sobre nosotros</div>
    <div class="flex justify-between mt-[36px]">
      <a v-for="(item, index) in aboutUsData" :key="index" :href="item.path">
        <img loading="lazy" :src="item.image" />
        <div class="text-[20px] leading-[24px] mt-[20px]">{{ item.title }}</div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import aboutUs from "@/assets/icons/common/about-us.png";
import aboutWork from "@/assets/icons/common/about-work.png";
import ourServices from "@/assets/icons/common/our-services.png";
import importGuide from "@/assets/icons/common/import-guide.png";

const aboutUsData = [
  {
    image: aboutUs,
    title: "Quiénes somos",
    path: "/quienes-somos/",
  },
  {
    image: aboutWork,
    title: "Cómo trabajamos",
    path: "/como-trabajarnos/",
  },
  {
    image: ourServices,
    title: "Nuestros servicios",
    path: "/servicios/",
  },
  {
    image: importGuide,
    title: "Importación de China",
    path: "/importacion-de-china-a-america-latina-la-guia-definitiva/",
  },
];
</script>

<style scoped lang="scss"></style>
