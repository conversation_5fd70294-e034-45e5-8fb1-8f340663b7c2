<template>
  <div class="w-full pt-[0.32rem] border-t-1 border-[#7F7F7F] mt-[2rem]">
    <div class="text-[0.44rem] leading-[0.44rem] font-medium">Descubre más sobre nosotros</div>
    <div class="flex flex-wrap gap-y-[0.32rem] justify-between mt-[0.56rem]">
      <a v-for="(item, index) in aboutUsData" :key="index" :href="item.path">
        <img loading="lazy" :src="item.image" class="w-[3.2rem]" />
        <div class="text-[0.32rem] leading-[0.32rem] mt-[0.2rem]">{{ item.title }}</div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import aboutUs from "@/assets/icons/common/about-us.png";
import aboutWork from "@/assets/icons/common/about-work.png";
import ourServices from "@/assets/icons/common/our-services.png";
import importGuide from "@/assets/icons/common/import-guide.png";

const aboutUsData = [
  {
    image: aboutUs,
    title: "Quiénes somos",
    path: "/h5/quienes-somos/",
  },
  {
    image: aboutWork,
    title: "Cómo trabajamos",
    path: "/h5/como-trabajarnos/",
  },
  {
    image: ourServices,
    title: "Nuestros servicios",
    path: "/h5/servicios/",
  },
  {
    image: importGuide,
    title: "Importación de China",
    path: "/h5/importacion-de-china-a-america-latina-la-guia-definitiva/",
  },
];
</script>

<style scoped lang="scss"></style>
