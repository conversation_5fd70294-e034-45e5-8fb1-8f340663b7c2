@font-face {
  font-family: "Roboto";
  src: url("../fonts/Roboto-Regular.ttf");
  font-weight: normal;
}

@font-face {
  font-family: "Roboto";
  src: url("../fonts/Roboto-Medium.ttf");
  font-weight: 500;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Roboto", sans-serif;
  font-weight: normal;
}

.vjs-big-play-button {
  width: 70px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 38px !important;
  border-radius: 14px !important;
  background-color: #e50113 !important;
  border: none !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.vjs-youtube-mobile .vjs-big-play-button {
  display: block !important;
}

.vjs-waiting .vjs-big-play-button {
  display: none !important;
}
.vjs-youtube-mobile.vjs-has-started .vjs-big-play-button {
  display: none !important;
}

.global-contact-btn {
  position: relative;
  transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

  .arrow-icon {
    transition: transform 0.8s cubic-bezier(0.25, 1, 0.5, 1),
      opacity 0.8s ease-in-out;
    opacity: 1;
  }

  &:hover {
    font-weight: 500;
    transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);

    .arrow-icon {
      content: url("@/assets/icons/quienes-somos/arrow-red.svg");
      transform: scale(1.28) translateX(6px);
    }
  }

  &:not(:hover) {
    transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);

    .arrow-icon {
      transform: scale(1) translateX(0);
      opacity: 1;
    }
  }
}

.global-navigation-btn {
  border: 1px solid #e50113;
  transition: all 0.3s ease;
  &:hover {
    background: #e50113 !important;
    color: #fff !important;
  }
}
