export const showToast = (message: any, iconUrl?: any) => {
  // 代码只在客户端执行
  if (process.client) {
    const globalToast = document.getElementById("global-toast");
    if (globalToast) {
      globalToast.style.opacity = "0";
      globalToast.remove();
    }
    const toast = document.createElement("div");
    toast.id = "global-toast";
    toast.style.position = "fixed";
    toast.style.top = "50%";
    toast.style.left = "0";
    toast.style.right = "0";
    toast.style.display = "flex";
    toast.style.justifyContent = "center";
    toast.style.opacity = "0";
    toast.style.zIndex = "99999999";
    toast.style.whiteSpace = "normal";
    toast.style.wordBreak = "break-word";
    toast.style.textAlign = "center";
    const toastInner = document.createElement("div");
    toastInner.style.display = "flex";
    toastInner.style.alignItems = "center";
    toastInner.style.backgroundColor = "#333";
    toastInner.style.color = "white";
    toastInner.style.padding = "10px";
    toastInner.style.borderRadius = "5px";
    toastInner.style.maxWidth = "90vw";
    toastInner.style.boxShadow = "0 0 10px rgba(0, 0, 0, 0.5)";

    // 有图标显示图标
    if (iconUrl) {
      const icon = document.createElement("img");
      icon.src = iconUrl;
      icon.style.width = "22px";
      icon.style.height = "22px";
      icon.style.marginRight = "4px";

      // 图标加载完成后再显示整个 toast
      icon.onload = () => {
        toast.style.opacity = "1";
      };

      toastInner.appendChild(icon);
    } else {
      // 如果没有图标，直接显示 toast
      setTimeout(() => {
        toast.style.opacity = "1";
      }, 100);
    }

    const text = document.createElement("span");
    text.innerHTML = message;
    toastInner.appendChild(text);

    // 3秒后动画隐藏 toast 并移除
    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        toast.remove();
      }, 500);
    }, 3000);

    // 将 toast 添加到 body 中
    toast.appendChild(toastInner);
    document.body.appendChild(toast);
  }
};
