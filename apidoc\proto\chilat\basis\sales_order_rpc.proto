syntax = "proto3";
package chilat.basis;


option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/sales_order_model.proto";
import "chilat/basis/param/sales_order_param.proto";
import "common.proto";

// 订单服务
service SalesOrderRpc {
    // 获取订单创建页的选项（查询费用选项列表等）
    rpc getPageOptions (common.EmptyParam) returns (SalesOrderPageOptionResp);
    //开单
    rpc createSo(SalesOrderCreateParam) returns (SalesOrderCreateResp);
    //开单预览（先用于计算阶梯价，传：SalesOrderLineParam soLineList，返回：SalesOrderLineModel soLineList）
    rpc previewSo(SalesOrderCreateParam) returns (SalesOrderDetailResp);
    //订单列表
    rpc pageList(SalesOrderPageParam) returns (SalesOrderPageListResp);
    //订单详情
    rpc soDetail(common.IdParam) returns (SalesOrderDetailResp);
    //取消订单
    rpc cancelSo(SalesOrderCancelParam) returns (common.ApiResult);

    //订单审核
    rpc auditSo(common.IdParam) returns (common.ApiResult);

    //支付查询订单
    rpc paySoDetail(common.IdParam) returns (SalesOrderPayResp);

    rpc queryOffLinePayInfo (QueryOffLinePayInfoParam) returns (QueryOffLinePayInfoResp);

    //mall端查询订单详情
    rpc mallOrderDetail(common.IdParam) returns (SalesOrderDetailResp);

    //获取订单行数据
    rpc getOrderLine(common.IdParam) returns (OrderLineResp);

    rpc updateOrderLineStatus(UpdateOrderLineStatusParam) returns (common.ApiResult);

    // 根据orderNo获取可用的状态列表----输入为orderNo
    rpc getValidOrderStatusByOrderNoList (common.IdParam) returns (GetOrderStatusListResp);

    // 修改订单状态
    rpc updateOrderStatus (UpdateOrderStatusParam) returns (common.ApiResult);
}