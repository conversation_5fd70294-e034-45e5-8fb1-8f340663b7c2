<template>
  <div class="w-[310px] pt-[120px]">
    <n-affix :trigger-top="56" class="w-[310px]" id="anchor-affix">
      <div class="text-[16px] leading-[16px] mb-[16px]">EN ESTA PÁGINA</div>
      <div class="page-anchor-container" ref="anchorContainerRef">
        <n-anchor
          :bound="500"
          :ignore-gap="false"
          :show-rail="true"
          :show-background="true"
        >
          <template v-if="anchorItems && anchorItems.length > 0">
            <n-anchor-link
              v-for="item in anchorItems"
              :key="item.title"
              :title="item.title"
              :href="item.href"
              class="ml-[16px]"
            />
          </template>
          <slot v-else></slot>
        </n-anchor>
      </div>
    </n-affix>
  </div>
</template>

<script setup lang="ts">
interface AnchorItem {
  title: string;
  href: string;
}

// 定义组件属性，参考naive-ui的文档
const props = defineProps({
  anchorItems: {
    type: Array as PropType<AnchorItem[]>,
    default: () => [],
  },
});

// 组件引用
const anchorContainerRef = ref(null);

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = window.setTimeout(() => fn(...args), delay);
  };
};

/**
 * 处理锚点滚动事件，调整活跃导航条的高度和位置
 */
const handleAnchorScroll = debounce(() => {
  nextTick(() => {
    // 获取当前活跃的锚点元素
    const activeLink = document.querySelector(".n-anchor-link--active");
    const activeBar = document.querySelector(
      ".n-anchor-rail__bar.n-anchor-rail__bar--active",
    );

    if (activeLink && activeBar && activeBar instanceof HTMLElement) {
      // 计算活跃锚点的高度
      const activeLinkHeight = activeLink.clientHeight;

      // 获取活跃锚点相对于其父元素的垂直位置偏移
      let offsetTop = 0;
      let currentElement = activeLink as HTMLElement;
      const parentAnchor = activeLink.closest(".n-anchor");

      // 计算相对于锚点容器的垂直偏移
      if (currentElement && parentAnchor) {
        const rect = currentElement.getBoundingClientRect();
        const parentRect = parentAnchor.getBoundingClientRect();
        offsetTop = rect.top - parentRect.top;
      }

      // 调整活跃导航条的高度和位置
      activeBar.style.height = `${activeLinkHeight}px`;
      activeBar.style.top = `${offsetTop}px`;
    }

    // 计算滚动到底部信息栏时 anchor-affix固定部分一起向上滚动
    const footerElement = document.getElementById("nav-footer");
    const rightAffix = document.getElementById("anchor-affix");
    if (footerElement && rightAffix) {
      const footerTop =
        footerElement.getBoundingClientRect().top + window.scrollY;
      const scrollTop =
        window.scrollY ||
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        0;
      const windowHeight =
        window.innerHeight ||
        document.documentElement.clientHeight ||
        document.body.clientHeight;

      // 计算距离底部的距离
      const distanceFromFooter = footerTop - (scrollTop + windowHeight);

      // 判断是否滚动到page-footer元素的位置
      if (distanceFromFooter <= 0) {
        // 计算right-affix的top值
        rightAffix.style.top = distanceFromFooter + "px";
      } else {
        // 如果没有滚动到footer的位置，保持right-affix的初始top值
        rightAffix.style.top = 56 + "px";
      }
    }
  });
}, 10);

/**
 * 添加DOM监视器，监听内容变化以及滚动事件
 */
const setupObservers = () => {
  // 观察DOM变化，以便在内容加载后重新计算高度
  const observer = new MutationObserver(handleAnchorScroll);

  if (anchorContainerRef.value) {
    observer.observe(anchorContainerRef.value, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
    });
  }

  // 监听滚动事件
  document.addEventListener("scroll", handleAnchorScroll);

  // 监听窗口大小变化
  window.addEventListener("resize", handleAnchorScroll);

  // 返回清理函数
  return () => {
    observer.disconnect();
    document.removeEventListener("scroll", handleAnchorScroll);
    window.removeEventListener("resize", handleAnchorScroll);
  };
};

onMounted(() => {
  // 设置观察者
  const cleanup = setupObservers();

  // 初始调用以设置初始状态
  handleAnchorScroll();

  // 组件卸载时清理
  onBeforeUnmount(cleanup);
});
</script>

<style scoped lang="scss">
:deep(.n-anchor) {
  font-size: 18px;
  line-height: 22px;
  --n-link-font-size: 18px;
  --n-link-line-height: 22px;
  --n-link-text-color-hover: #e50113;
  --n-link-text-color-pressed: #e50113;
  --n-rail-width: 5px;
  --n-link-padding: 17px 10px;
  --n-rail-color: #f2f2f2;
}

:deep(.n-anchor-link-background) {
  height: fit-content !important;
  width: initial !important;
  max-width: initial !important;
}

:deep(.n-anchor .n-anchor-link) {
  font-size: 18px;
  line-height: 22px;
}

.page-anchor-container {
  position: relative;
  width: 100%;
  height: 100%;
}

:deep(.n-anchor-link--active) {
  transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-anchor-rail__bar.n-anchor-rail__bar--active) {
  transition: height 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    top 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.n-anchor-link .n-anchor-link__title) {
  white-space: normal !important;
  overflow: visible !important;
  padding-right: 0 !important;
}

:deep(.n-anchor-link--active) {
  background-color: #f2f2f2 !important;
}
</style>
