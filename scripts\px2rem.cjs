/**
 * px转rem转换脚本
 * 自动扫描移动端文件并将px转换为rem
 */

const fs = require("fs");
const path = require("path");
const glob = require("glob");

// 基准值 - 与设计稿对应
const BASE_SIZE = 50; // 基于375px设计稿

// 匹配px的正则表达式
const PX_PATTERN = /(\d+(\.\d+)?)px/g;

// 需要处理的文件扩展名
const FILE_EXTENSIONS = [".vue", ".css", ".scss", ".js", ".ts"];

// 移动端文件路径模式
const MOBILE_PATTERNS = [
  "src/pages/h5/**/*", // h5路径下所有文件
  "src/**/*mobile*/**/*", // 包含mobile的目录下所有文件
  "src/**/*Mobile*/**/*", // 包含Mobile的目录下所有文件
  "src/**/*mobile*.*", // 文件名包含mobile
  "src/**/*Mobile*.*", // 文件名包含Mobile
];

// 排除的文件
const EXCLUDE_PATTERNS = ["node_modules/**", "dist/**", ".nuxt/**"];

// 转换px为rem的函数
function pxToRem(match, numStr) {
  const num = parseFloat(numStr);
  // 忽略0px
  if (num === 0) return "0";

  // 先保留4位小数
  const remValue = (num / BASE_SIZE).toFixed(4);

  // 去除尾部多余的0
  return `${parseFloat(remValue)}rem`;
}

// 处理文件内容中的px
function processPxInContent(content, file) {
  // 匹配style标签中的内容
  let styleTagPattern = /<style[^>]*>([\s\S]*?)<\/style>/gi;
  let newContent = content;

  // 替换style标签中的px
  newContent = newContent.replace(styleTagPattern, (match, styleContent) => {
    const convertedStyle = styleContent.replace(PX_PATTERN, pxToRem);
    return match.replace(styleContent, convertedStyle);
  });

  // 处理内联样式
  const styleAttrPattern = /style=["'](.*?)["']/gi;
  newContent = newContent.replace(styleAttrPattern, (match, styleContent) => {
    if (styleContent.includes("px")) {
      const convertedStyle = styleContent.replace(PX_PATTERN, pxToRem);
      return match.replace(styleContent, convertedStyle);
    }
    return match;
  });

  // 如果是CSS文件，直接处理所有内容
  if (
    content === newContent &&
    (path.extname(file) === ".css" || path.extname(file) === ".scss")
  ) {
    newContent = content.replace(PX_PATTERN, pxToRem);
  }

  return newContent;
}

// 处理JavaScript中的px
function processPxInJS(content) {
  // 处理JS中的字符串，如 '10px' 或 "width: 20px"
  const jsStringPattern = /(['"`])(.*?)\1/g;

  return content.replace(jsStringPattern, (match, quote, innerContent) => {
    if (innerContent.includes("px")) {
      const convertedContent = innerContent.replace(PX_PATTERN, pxToRem);
      return `${quote}${convertedContent}${quote}`;
    }
    return match;
  });
}

// 获取所有符合条件的文件
function getFilesToProcess() {
  const allFiles = [];

  MOBILE_PATTERNS.forEach((pattern) => {
    const files = glob.sync(pattern, {
      ignore: EXCLUDE_PATTERNS,
      nodir: true,
    });

    files.forEach((file) => {
      const ext = path.extname(file);
      if (FILE_EXTENSIONS.includes(ext)) {
        allFiles.push(file);
      }
    });
  });

  // 去重
  return [...new Set(allFiles)];
}

// 处理单个文件
function processFile(file) {
  try {
    const content = fs.readFileSync(file, "utf8");
    let newContent = content;

    // 根据文件类型处理
    const ext = path.extname(file);

    if (ext === ".vue" || ext === ".css" || ext === ".scss") {
      newContent = processPxInContent(content, file);
    }

    if (ext === ".js" || ext === ".ts" || ext === ".vue") {
      newContent = processPxInJS(newContent);
    }

    // 检查内容是否有变化
    if (content !== newContent) {
      fs.writeFileSync(file, newContent, "utf8");
      return true;
    }

    return false;
  } catch (error) {
    console.error(`处理文件 ${file} 时出错:`, error);
    return false;
  }
}

// 主函数
function main() {
  console.log("正在查找移动端文件...");
  const files = getFilesToProcess();
  console.log(`找到 ${files.length} 个需要处理的文件`);

  let convertedCount = 0;

  files.forEach((file, index) => {
    process.stdout.write(`处理文件 [${index + 1}/${files.length}]: ${file} `);
    const changed = processFile(file);
    if (changed) {
      process.stdout.write("✓\n");
      convertedCount++;
    } else {
      process.stdout.write("→\n");
    }
  });

  console.log(
    `\n完成! 共处理 ${files.length} 个文件，转换了 ${convertedCount} 个文件中的px为rem。`
  );
}

// 执行主函数
main();
