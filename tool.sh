#! /bin/bash

printHelp() {
    echo -e "-g/--gen: \033[31m 编译Protobuf并生成TS \033[0m"
    echo -e "-p/--prod: \033[31m 同步代码并提交SVN \033[0m"
    echo -e "-r/--run: \033[31m 运行当前分支 \033[0m"
    echo -e "-px2rem: \033[31m 将移动端页面的px转换为rem \033[0m"
}

devRun() {
  br=`git branch | grep "*"`
	currentbranch=${br/* /}
  if [ "$currentbranch"x != "feature/dev"x ];then
    echo -e "\033[31m 请切换到: feature/dev 分支再编译提交 \033[0m"
    exit 1
  fi
  start http://**************:9090/jenkins/job/b2b-chilat-mall-main/
}

# px转rem转换函数
convertPxToRem() {
  echo -e "\033[32m 开始转换移动端页面的px为rem... \033[0m"
  
  # 确保依赖已安装
  if [ ! -d "node_modules/glob" ]; then
    echo "安装依赖..."
    pnpm add glob --save-dev
  fi
  
  # 调用外部JavaScript脚本
  node scripts/px2rem.cjs
  
  echo -e "\033[32m px转rem转换完成! \033[0m"
}

generateProto() {
  dir=./src/services
  mkdir -p $dir
  # protobufServices=./apidoc/proto/mall/commodity/goods_info.proto
  # protobufServices=./apidoc/proto/mall/main/mall_config.proto
  # protobufServices=./apidoc/proto/mall/pages/goods_list_page.proto
  # protobufServices=./apidoc/proto/mall/pages/home_page.proto
  # protobufServices=./apidoc/proto/mall/pages/search_page.proto
  # protobufServices=./apidoc/proto/mall/pages/goods_detail_page.proto
  # protobufServices=./apidoc/proto/mall/pages/home_page.proto
  # protobufServices=./apidoc/proto/mall/marketing/coupon_detail.proto
  protobufServices=./apidoc/proto/mall/pages/article_page.proto
  
	protoc --plugin=protoc-gen-ts_proto=.\\node_modules\\.bin\\protoc-gen-ts_proto.cmd --ts_proto_out=:$dir $protobufServices -I ./apidoc/proto
	/usr/bin/find $dir -type f -exec sed -i 's/= require("long")/from "long"/g' {} \;
}

replaceImageAlt() {
  TARGET_DIR="./src/pages"
  FILE_EXT="vue"  # 要筛选的文件后缀
  declare -A altMap=(
  
  )

  if [ ! -d "$TARGET_DIR" ]; then
    echo "错误：目录 $TARGET_DIR 不存在！"
    exit 1
  fi
  

  # 使用 find 命令递归查找所有 .vue 文件
  find "$TARGET_DIR" -type f -name "*.vue" | while read -r vue_file; do
    # 确认是文件且后缀为 .vue（双重验证，确保准确性）
    if [ -f "$vue_file" ] && [[ "$vue_file" == *.vue ]]; then
      # echo -e "存在 文件 '$vue_file'"
      # rm -rf $vue_file
      temp=${vue_file#./src/pages/}
      # 再移除后面的/index.vue
      result=${temp%/index.vue}
      if [ -v altMap["$result"] ]; then
        newAlt=${altMap[$result]}
        echo -e "存在 key '$result'，值为：$newAlt, 在文件：$vue_file"
        sed -i -E "s/(alt=)\"[^\"]*\"/\1\"$newAlt\"/g" "$vue_file"
      else
        echo -e "不存在 key '$result'"
      fi
    fi
  done

  

  echo "====================================="
  echo "遍历完成，已列出所有 .vue 文件"
}

# 查找
findImageAlt() {
  TARGET_DIR="./src/pages"
  FILE_EXT="vue"  # 要筛选的文件后缀
  declare -A altMap=(
    ["alojamiento-y-comida-en-yiwu"]="alojamiento y comida en yiwu"
    ["blog"]="blog"
    ["busqueda-de-proveedores-y-productos"]="busqueda de proveedores y productos"
    ["canton"]="canton"
    ["chilat-team"]="chilat team"
    ["como-trabajarnos"]="como trabajarnos"
    ["consolidacion-de-cargas-y-logistica"]="consolidacion de cargas y logistica"
    ["contacto"]="contacto"
    ["donde-queda-yiwu"]="donde queda yiwu"
    ["elijanos"]="elijanos"
    ["feria-de-canton"]="feria de canton"
    ["feria-de-yiwu"]="feria de yiwu"
    ["guangzhou-hotel-suministra-mercado-mayorista"]="guangzhou hotel suministra mercado mayorista"
    ["guia-del-mercado-de-yiwu"]="guia del mercado de yiwu"
    ["h5/alojamiento-y-comida-en-yiwu"]="alojamiento y comida en yiwu"
    ["h5/blog"]="blog"
    ["h5/busqueda-de-proveedores-y-productos"]="busqueda de proveedores y productos"
    ["h5/canton"]="canton"
    ["h5/chilat-team"]="chilat team"
    ["h5/como-trabajarnos"]="como trabajarnos"
    ["h5/consolidacion-de-cargas-y-logistica"]="consolidacion de cargas y logistica"
    ["h5/contacto"]="contacto"
    ["h5/donde-queda-yiwu"]="donde queda yiwu"
    ["h5/elijanos"]="elijanos"
    ["h5/feria-de-canton"]="feria de canton"
    ["h5/feria-de-yiwu"]="feria de yiwu"
    ["h5/guangzhou-hotel-suministra-mercado-mayorista"]="guangzhou hotel suministra mercado mayorista"
    ["h5/guia-del-mercado-de-yiwu"]="guia del mercado de yiwu"
    ["h5/importacion-de-china-a-america-latina-la-guia-definitiva"]="importacion de china a america latina la guia definitiva"
    ["h5/mercado-circundante-de-guangzhou"]="mercado circundante de guangzhou"
    ["h5/mercado-de-guangzhou"]="mercado de guangzhou"
    ["h5/mercado-de-productos-de-cuero-de-guangzhou"]="mercado de productos de cuero de guangzhou"
    ["h5/mercado-de-ropa-de-guangzhou"]="mercado de ropa de guangzhou"
    ["h5/mercados-al-por-mayor-de-telas-de-guangzhou"]="mercados al por mayor de telas de guangzhou"
    ["h5/mercados-de-cosmeticos-al-por-mayor-de-guangzhou"]="mercados de cosmeticos al por mayor de guangzhou"
    ["h5/mercados-de-electronicos-y-computadores-de-guangzhou"]="mercados de electronicos y computadores de guangzhou"
    ["h5/mercados-de-joyeria-de-guangzhou"]="mercados de joyeria de guangzhou"
    ["h5/mercados-de-juguetes-y-regalos-de-guangzhou"]="mercados de juguetes y regalos de guangzhou"
    ["h5/mercados-de-zapatos-de-guangzhou"]="mercados de zapatos de guangzhou"
    ["h5/preguntas-frecuentes-sobre-yiwu"]="preguntas frecuentes sobre yiwu"
    ["h5/quienes-somos"]="quienes somos"
    ["h5/servicio-de-garantia-de-importacion"]="servicio de garantia de importacion"
    ["h5/servicios"]="servicios"
    ["h5/socio-de-negocios"]="socio de negocios"
    ["h5/tienda/[id].vue"]="tienda/[id].vue"
    ["h5/tiendas-panoramicas-en-3d"]="tiendas panoramicas en 3d"
    ["h5/viaja-al-mercado-de-yiwu"]="viaja al mercado de yiwu"
    ["h5/viajar-a-china"]="viajar a china"
    ["h5/vip"]="vip"
    ["h5/yiwu"]="yiwu"
    ["h5/yiwu-fair"]="yiwu fair"
    ["importacion-de-china-a-america-latina-la-guia-definitiva"]="importacion de china a america latina la guia definitiva"
    ["mercado-circundante-de-guangzhou"]="mercado circundante de guangzhou"
    ["mercado-de-guangzhou"]="mercado de guangzhou"
    ["mercado-de-productos-de-cuero-de-guangzhou"]="mercado de productos de cuero de guangzhou"
    ["mercado-de-ropa-de-guangzhou"]="mercado de ropa de guangzhou"
    ["mercados-al-por-mayor-de-telas-de-guangzhou"]="mercados al por mayor de telas de guangzhou"
    ["mercados-de-cosmeticos-al-por-mayor-de-guangzhou"]="mercados de cosmeticos al por mayor de guangzhou"
    ["mercados-de-electronicos-y-computadores-de-guangzhou"]="mercados de electronicos y computadores de guangzhou"
    ["mercados-de-joyeria-de-guangzhou"]="mercados de joyeria de guangzhou"
    ["mercados-de-juguetes-y-regalos-de-guangzhou"]="mercados de juguetes y regalos de guangzhou"
    ["mercados-de-zapatos-de-guangzhou"]="mercados de zapatos de guangzhou"
    ["preguntas-frecuentes-sobre-yiwu"]="preguntas frecuentes sobre yiwu"
    ["quienes-somos"]="quienes somos"
    ["servicio-de-garantia-de-importacion"]="servicio de garantia de importacion"
    ["servicios"]="servicios"
    ["socio-de-negocios"]="socio de negocios"
    ["tienda/[id].vue"]="tienda/[id].vue"
    ["tiendas-panoramicas-en-3d"]="tiendas panoramicas en 3d"
    ["viaja-al-mercado-de-yiwu"]="viaja al mercado de yiwu"
    ["viajar-a-china"]="viajar a china"
    ["vip"]="vip"
    ["yiwu"]="Las ventajas únicas del mercado de Yiwu"
    ["yiwu-fair"]="yiwu fair"
  )

  if [ ! -d "$TARGET_DIR" ]; then
    echo "错误：目录 $TARGET_DIR 不存在！"
    exit 1
  fi

  altValue="TODO 商品图片ALT"

  # 使用 find 命令递归查找所有 .vue 文件
  find "$TARGET_DIR" -type f -name "*.vue" | while read -r vue_file; do
    # 确认是文件且后缀为 .vue（双重验证，确保准确性）
    if [ -f "$vue_file" ] && [[ "$vue_file" == *.vue ]]; then
      
      # 获取新的alt值
      local new_alt=""
      temp=${vue_file#./src/pages/}
      # 再移除后面的/index.vue
      result=${temp%/index.vue}
      if [ -v altMap["$result"] ]; then
        new_alt=${altMap[$result]}
      fi
      # ChatGPT-判断是否有缺少 alt 的 <img>
      if grep -q "<img" "$vue_file" && ! grep -q "<img[^>]*alt=" "$vue_file"; then
        echo "⚠️ $result, $new_alt, 存在 <img> 没有 alt，正在修复..."
        # 只在没有 alt 的 <img> 后面插入 alt="xxx"
        # sed -i "s|<img\([^>]*\)>|<img\1 alt=\"${altValue}\">|g" "$vue_file"
        sed -i "s|<img\([^>]*\)[[:space:]]*/>|<img\1 alt=\"$new_alt\">|g" "$vue_file"
      else
        echo "✅ $vue_file 中的 <img> 都有 alt"
        sed -i -E "s/(alt=)\"[^\"]*\"/\1\"$new_alt\"/g" "$vue_file"
      fi
    fi
  done

  

  echo "====================================="
  echo "遍历完成，已列出所有 .vue 文件"

}


prodSync() {
  # 判断分支
  br=`git branch | grep "*"`
	currentbranch=${br/* /}
  if [ "$currentbranch"x != "master"x ];then
    echo -e "\033[31m 请切换到：master 分支再编译提交 \033[0m"
    exit 1
  fi

  dir=/c/workspace/project/chilat/chilat-main-ssr
	/usr/bin/rm -rf $dir
  /usr/bin/mkdir -p /c/workspace/project/chilat/chilat-main-ssr/src
	/usr/bin/cp -rf src/* /c/workspace/project/chilat/chilat-main-ssr/src
  /usr/bin/cp ./.env.development $dir
	/usr/bin/cp ./.env.production $dir
	/usr/bin/cp ecosystem.config.cjs $dir
	/usr/bin/cp ./.npmrc $dir
	/usr/bin/cp nuxt.config.ts $dir
	/usr/bin/cp package.json $dir
	/usr/bin/cp pnpm-lock.yaml $dir
	/usr/bin/cp tsconfig.json $dir
	/usr/bin/cp uno.config.ts $dir
	/usr/bin/cp Dockerfile $dir
	/usr/bin/cp DockerRun.sh $dir
	/usr/bin/cp startService.sh $dir
	/usr/bin/cp stopService.sh $dir
	/usr/bin/cp README.md $dir
  # 提交SVN代码
  cd /c/workspace/project/chilat
  read -p "请输入提交内容:" cc
  svn st | grep ! | cut -d! -f2| sed 's/^ *//' | sed 's/^/"/g' | sed 's/$/"/g' | xargs svn rm
	svn add . --no-ignore --force
  svn commit -m "$cc"
  echo -e "\033[32m SVN提交完成 \033[0m"
  start /c/workspace/project/chilat
  start https://jenkins81.xpormayor.com.mx/job/%E3%80%90%E5%89%8D%E7%AB%AF-Vue%E3%80%91Chilat%20Main%20SSR%EF%BC%88Chilat%E4%B8%BB%E7%AB%99%EF%BC%89/
  cd -
}

case $1 in
    "-h" | "--help")
        printHelp
    ;;
    "-g" | "--gen")
        generateProto
    ;;
    "-p" | "--prod")
        prodSync
    ;;
    "-d" | "--dev")
        devRun
    ;;
    "-px2rem")
        convertPxToRem
    ;;
    "-alt")
        replaceImageAlt
    ;;
    "-falt")
        findImageAlt
    ;;
    *)
        printHelp
    ;;
esac