<template>
  <div class="w-full bg-white">
    <div class="page-header text-[#fff] overflow-auto px-[0.4rem] py-[0.6rem]">
      <Breadcrumb :items="breadcrumbItems" />
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">
        Mercados de cosméticos al por mayor de Guangzhou
      </div>
    </div>
    <div class="pb-[2.4rem] px-[0.4rem]">
      <div class="pt-[1.4rem]" id="ciudad-meibo">
        <div class="text-[0.56rem] leading-[0.64rem] font-medium">Ciudad Meibo</div>
        <div class="text-[0.36rem] leading-[0.54rem] mt-[1rem] flex flex-col gap-[0.54rem]">
          <div>
            Meibo Center (en chino es meibo cheng) se encuentra en el cruce entre la autopista del aeropuerto, la
            carretera este de Guangyuan y la autopista Beierhuan. Muchos autobuses pasan por el centro y una estación de
            metro se encuentra a poca distancia. El mayor mercado mayorista profesional de productos cosméticos y de
            belleza del área Asia-Pacífico. Es uno de los 12 proyectos clave de comercio apoyados por el gobierno de
            Guangzhou. La ciudad Meibo de Guangzhou tiene un área total de 50.000 metros cuadrados para la exposición
            comercial.
          </div>
          <div>
            La ciudad Meibo de Guangzhou tiene regularmente feria de belleza y productos cosméticos en primavera y otoño
            cada año. Encontrará todos los cosméticos de marca conocidos fabricados en China y productos de marcas
            famosas de Europa, América y el sudeste asiático. La Feria de Guangzhou Meibo atrae a fabricantes y
            distribuidores, revendedores de todo el mundo. Varios tipos de cosméticos de salón de belleza y productos
            relacionados de exposiciones especiales se llevarán a cabo de forma regular. La ciudad Meibo de Guangzhou se
            ha convertido en un puente efectivo entre proveedores y usuarios.
          </div>
          <div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span> Campo comercial: productos de belleza y peluquería </span>
            </div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span> Dirección: No 121, Guangyuan W. Road, Distrito Baiyun, Guangzhou </span>
            </div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span>Número de teléfono de la oficina: (020) 61149830/61149831</span>
            </div>
          </div>
          <img
            loading="lazy"
            class="mt-[0.14rem]"
            src="@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/canton1.png"
            alt="mercados de cosmeticos al por mayor de guangzhou"
          />
        </div>
      </div>
      <div class="pt-[1.6rem]" id="plaza-xinfa">
        <div class="text-[0.56rem] leading-[0.64rem] font-medium">Plaza Xinfa</div>
        <div class="text-[0.36rem] leading-[0.54rem] mt-[1rem] flex flex-col gap-[0.54rem]">
          <div>
            Cubriendo más de 50.000 metros cuadrados, la plaza Xinfa tiene 1.700 oficinas y stands. Rodeado por
            Guangdong Video City, Tianlong Circuit City y la plaza Yifa, este mercado también ofrece bancos, almacenes,
            estacionamientos y estaciones de carga.
          </div>
          <div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span>
                Campo comercial: accesorios para el cabello y la belleza, cosméticos, productos químicos y productos de
                limpieza
              </span>
            </div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span> Dirección: No 138, Airport Road, Baiyun District, Guangzhou </span>
            </div>
            <div class="flex items-start gap-[0.16rem] ml-[0.16rem]">
              <span style="font-size: 0.6rem">&middot;</span>
              <span> Número de teléfono de la oficina: 020-86551837 </span>
            </div>
          </div>

          <img
            loading="lazy"
            src="@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/canton2.png"
            alt="mercados de cosmeticos al por mayor de guangzhou"
            class="mt-[0.14rem]"
          />
        </div>
      </div>
      <mobile-market-nav />
      <mobile-page-anchor :anchorItems="anchorItems" />
    </div>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/common/home.svg";

useHead({
  title: "Mercados de cosmeticos al por mayor de Guangzhou - Chilat",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/`,
    },
  ],
});

const breadcrumbItems = [
  { link: "/h5", icon: homeIcon, alt: "home" },
  { text: "Ferias y mercado" },
  { link: "/h5/canton", text: "Cantón" },
  {
    link: "/h5/mercado-de-guangzhou",
    text: "Mercado mayorista en Guangzhou",
  },
  {
    link: "/h5/mercados-de-cosmeticos-al-por-mayor-de-guangzhou",
    text: "Mercados de cosméticos al por mayor de Guangzhou",
  },
];

const anchorItems = [
  {
    title: "Ciudad Meibo",
    href: "#ciudad-meibo",
  },
  {
    title: "Plaza Xinfa",
    href: "#plaza-xinfa",
  },
];
</script>

<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100% 100%;
  background-image: url("@/assets/icons/mercados-de-cosmeticos-al-por-mayor-de-guangzhou/mobile-header-bg.png");
  background-repeat: no-repeat;
}
</style>
