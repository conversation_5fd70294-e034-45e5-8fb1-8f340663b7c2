<template>
  <div class="w-full bg-white">
    <n-carousel
      :draggable="true"
      :autoplay="true"
      :show-dots="true"
      :interval="8000"
      class="homepage-carousel"
      dot-placement="bottom"
      dot-type="dot"
      :show-arrow="false"
      trigger="hover"
    >
      <n-carousel-item>
        <div class="page-header py-[0.48rem] px-[0.4rem]">
          <div class="text-[0.88rem] leading-[1.2rem] font-medium">¿Cómo importar desde China?</div>
          <div class="w-[1.92rem] ml-auto mt-[3rem] flex flex-col gap-[0.2rem] items-center" @click="onOpenVideo()">
            <img
              loading="lazy"
              src="@/assets/icons/home/<USER>"
              alt="video"
              class="w-[1.28rem] h-[1.28rem]"
            />
            <div class="w-[0.12rem] h-[0.12rem] border-1 border-[#e50113] rounded-full"></div>
            <span class="text-[0.26rem] leading-[0.26rem]">Conócenos Aquí</span>
          </div>
          <div class="text-[0.32rem] leading-[0.42rem] mt-[0.54rem] text-right flex flex-col relative">
            <img
              loading="lazy"
              src="@/assets/icons/home/<USER>"
              alt="stroke"
              class="w-[2.08rem] absolute top-[-0.28rem] right-[-0.24rem]"
            />
            <span
              >Desde encontrar los productos<br />
              hasta el despacho, le brindamos<br />
              una solución completa.</span
            >
            <div class="mt-[0.12rem]">—— CHILAT</div>
          </div>
        </div>
      </n-carousel-item>
      <n-carousel-item>
        <div class="second-carousel-wrapper text-white text-center">
          <a href="/h5/tiendas-panoramicas-en-3d" class="w-full h-full block pt-[3.72rem] px-[0.4rem]">
            <div class="text-[0.8rem] leading-[1.2rem] font-medium">Tiendas<br />panorámicas en</div>
            <div class="text-[1rem] leading-[1rem] mt-[0.32rem] font-medium">3D</div>
            <div class="text-[0.32rem] leading-[0.6rem] mt-[0.96rem]">Miles de<br />productos presentados online</div>
          </a>
          <div class="w-[2rem] h-[1rem] absolute bottom-[0.8rem] left-1/2 -translate-x-1/2"></div>
        </div>
      </n-carousel-item>
    </n-carousel>
    <div class="pt-[0.8rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Usted necesita</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>
      <div class="flex justify-between flex-wrap gap-y-[0.32rem] mt-[0.92rem]">
        <a v-for="(item, index) in requirementInfo" :key="index" :href="item.path" class="w-full flex gap-[0.2rem]">
          <img loading="lazy" :src="item.icon" :alt="item.title" class="w-[0.76rem] h-[0.76rem] flex-shrink-0" />
          <div class="pb-[0.32rem] border-b-1 border-[#F2F2F2]">
            <div class="text-[0.36rem] leading-[0.44rem] text-[#333333] h-[0.88rem] flex items-center">
              {{ item.title }}
            </div>
            <div class="text-[0.28rem] leading-[0.42rem] text-[#7F7F7F] mt-[0.24rem]" v-html="item.content"></div>
          </div>
        </a>
      </div>
    </div>
    <div class="pt-[1.2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Elíjanos</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>
      <div class="flex justify-between flex-wrap gap-y-[0.4rem] mt-[0.6rem]">
        <a
          v-for="(item, index) in chooseInfo"
          :href="item.path"
          class="w-full h-[7.04rem] text-center border-1 border-[#7F7F7F] rounded-[0.4rem] overflow-hidden relative"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            :alt="item.title"
            class="w-[1.24rem] h-[1.24rem] absolute top-[2.72rem] right-[0.24rem] z-1"
          />

          <img loading="lazy" :src="item.icon" :alt="item.title" class="w-full" />
          <div class="text-[0.4rem] leading-[0.48rem] text-[#333333] mt-[0.54rem] px-[0.24rem]">
            {{ item.title }}
          </div>
          <div class="text-[0.28rem] leading-[0.32rem] text-[#7F7F7F] mt-[0.2rem] px-[0.24rem]">
            {{ item.content }}
          </div>
        </a>
      </div>
    </div>
    <div class="pt-[2rem] px-[0.4rem]">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Nuestros servicios</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>
      <div class="mt-[0.72rem] flex flex-col justify-between relative">
        <!-- 右侧服务列表 -->
        <div
          v-for="(item, index) in serviceInfo"
          :key="item.title"
          class="service-item group"
          @click="handleServiceHover(item, index)"
        >
          <div
            class="py-[0.44rem] transition-colors duration-500 cursor-pointer"
            :class="[pageData.activeServices.includes(item.title) ? 'text-[#e50113]' : '']"
          >
            <div class="flex items-center justify-between">
              <div class="flex">
                <span
                  class="text-[#999] text-[0.32rem] leading-[0.44rem] mr-[0.28rem]"
                  :class="[pageData.activeServices.includes(item.title) ? 'text-[#e50113]' : '']"
                  >{{ String(index + 1).padStart(2, "0") }}</span
                >
                <span class="text-[0.36rem] leading-[0.44rem]">{{ item.title }}</span>
              </div>
              <img
                v-if="!pageData.activeServices.includes(item.title)"
                loading="lazy"
                alt="expand"
                class="w-[0.44rem]"
                src="@/assets/icons/common/expand-red.svg"
              />
              <img
                v-else
                loading="lazy"
                alt="collapse"
                class="w-[0.44rem]"
                src="@/assets/icons/common/collapse-red.svg"
              />
            </div>
            <!-- 展开的内容 -->
            <div
              class="w-full overflow-hidden transition-all duration-500 pl-[0.64rem]"
              :class="[
                pageData.activeServices.includes(item.title)
                  ? 'mt-[0.2rem]  max-h-[8rem] opacity-100'
                  : 'max-h-0 opacity-0',
              ]"
            >
              <div class="flex w-full">
                <div class="text-[0.32rem] leading-[0.44rem] text-[#666] w-[4.8rem]">
                  {{ item.content }}
                </div>
                <a @click.stop :href="item.path" class="w-[0.88rem] items-center flex-shrink-0">
                  <img
                    loading="lazy"
                    src="@/assets/icons/quienes-somos/arrow-line.svg"
                    alt="arrow"
                    class="w-[0.88rem]"
                  />
                </a>
              </div>
              <img loading="lazy" class="w-[6.06rem] h-[3.8rem] mt-[0.2rem]" :src="item.icon" :alt="item.title" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pt-[1.6rem] px-[0.4rem] pb-[0.8rem]">
      <n-carousel
        :space-between="10"
        :loop="false"
        draggable
        slides-per-view="auto"
        :current-index="pageData.currentVideoIndex"
        @update:current-index="pageData.currentVideoIndex = $event"
        class="video-carousel"
        :show-arrow="false"
      >
        <n-carousel-item
          class="!w-[5.76rem] h-[3.24rem] rounded-[0.08rem] relative overflow-hidden flex-shrink-0"
          v-for="(video, index) in videoInfo"
          :key="video.id"
        >
          <div>
            <div class="w-[5.76rem] h-[3.24rem] rounded-[0.08rem] overflow-hidden">
              <video-you-tube
                :width="288"
                :height="162"
                :youtubeId="video.id"
                :titleCh="video.titleCh"
                :poster="video.poster"
              ></video-you-tube>
            </div>
            <div class="text-[0.36rem] leading-[0.36rem] mt-[0.4rem]">
              {{ video.title }}
            </div>
          </div>
        </n-carousel-item>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li v-for="index of total" :key="index" :class="{ ['is-active']: index - 1 <= currentIndex }"></li>
          </ul>
        </template>
      </n-carousel>
    </div>
    <div class="pt-[2rem] px-[0.4rem] overflow-hidden">
      <div class="text-[0.56rem] leading-[0.68rem]">Chilat equipo</div>
      <div class="text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.36rem]">
        Ahora tenemos un equipo de 80 personas en Yiwu y hay 6 personas en la oficina de Guangzhou, incluyendo
        <span class="font-medium">40 personas</span> de habla hispana. Ahora ya somos la empresa mas grande en Yiwu que
        presta servicios a países de habla hispana. Trabajamos para pymes, corpora- ciones multinacionales y proveedores
        de Carrefour, Disney, Jumbo, India Style, Cencosud, Alibaba, entre otros. En todo momento y a distinta escala de
        negocios podemos responder en forma eficaz.
      </div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
      <div class="flex flex-col gap-[0.4rem] mt-[1.2rem] pb-[1.6rem] custom-cursor">
        <n-carousel
          draggable
          show-arrow
          :loop="false"
          :space-between="24"
          slides-per-view="auto"
          class="team-carousel"
          :current-index="pageData.currentTeamIndex"
          @update:current-index="pageData.currentTeamIndex = $event"
        >
          <n-carousel-item
            class="!w-[5rem] h-[6.2rem] rounded-[0.32rem] relative overflow-hidden flex-shrink-0"
            v-for="item in teamInfo"
            :key="item.name"
          >
            <a :href="item.path" class="inline-block w-[5rem] h-[6.2rem] text-white rounded-[0.32rem] relative">
              <!-- 背景层（渐变 + 图片） -->
              <div
                class="absolute inset-0 w-full h-full"
                :style="`background: linear-gradient(180deg, rgba(0, 0, 0, 0) 50.08%, rgba(0, 0, 0, 0.6) 100%), url(${item.image}) center/cover no-repeat;`"
              ></div>

              <!-- 文字内容 -->
              <div class="relative z-10 h-full flex flex-col pb-[0.24rem] mt-[4.4rem]">
                <div class="inline-flex items-center px-[0.24rem]">
                  <div class="text-[0.32rem] leading-[0.32rem]">
                    {{ item.name }}
                  </div>
                  <img
                    loading="lazy"
                    src="@/assets/icons/home/<USER>"
                    alt="arrow"
                    class="w-[0.56rem] h-[0.56rem] hover:scale-128 hover:translate-x-[0.08rem] transition-all duration-500"
                  />
                </div>

                <div class="text-[0.2rem] leading-[0.26rem] mt-[0.06rem] px-[0.24rem]">
                  {{ item.position }}
                </div>
              </div>
            </a>
          </n-carousel-item>
          <template #arrow="{ prev, next }">
            <div class="custom-arrow">
              <icon-card
                @click="prev"
                name="fe:arrow-left"
                size="22"
                :color="pageData.currentTeamIndex === 0 ? '#D9D9D9' : '#e50113'"
                class="mr-[0.6rem]"
              >
              </icon-card>
              <icon-card
                @click="next"
                name="fe:arrow-right"
                size="22"
                :color="pageData.currentTeamIndex === teamInfo.length - 1 ? '#D9D9D9' : '#e50113'"
              >
              </icon-card>
            </div>
          </template>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li v-for="index of total" :key="index" :class="{ ['is-active']: index - 1 <= currentIndex }"></li>
            </ul>
          </template>
        </n-carousel>
      </div>
      <a class="flex justify-center mt-[1.6rem]" href="/h5/quienes-somos/">
        <n-button
          size="large"
          color="#fff"
          text-color="#e50113"
          class="global-navigation-btn w-[4.56rem] rounded-[10rem] h-[1rem]"
        >
          <span class="text-[0.36rem] leading-[0.36rem]">Conócenos</span>
        </n-button>
      </a>
    </div>
    <div class="pt-[2rem] px-[0.4rem] pb-[0.6rem] relative">
      <div class="text-[0.56rem] leading-[0.68rem] text-center">Testimonials</div>
      <div class="w-[1rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"></div>
      <n-carousel
        draggable
        :loop="true"
        :show-dots="false"
        :show-arrow="true"
        :space-between="20"
        ref="feedbackCarousel"
        :current-index="pageData.currentFeedbackIndex"
        @update:current-index="pageData.currentFeedbackIndex = $event"
        class="feedback-carousel mt-[1.2rem] h-[9.6rem]"
      >
        <n-carousel-item v-for="(item, index) in feedbackInfo" :key="item.title">
          <div class="flex flex-col pt-[0.48rem] px-[0.24rem] relative" :style="`height:${item.height}`">
            <img
              :src="item.bgUrl"
              class="absolute top-0 left-0 w-full h-full object-cover z-0"
              loading="lazy"
              :alt="item.title"
            />
            <div class="text-[0.32rem] leading-[0.48rem] relative z-10">
              {{ item.content }}
            </div>
            <div class="flex items-center gap-[0.28rem] mt-[0.4rem] relative z-10">
              <img loading="lazy" :src="item.icon" class="w-[1.36rem] h-[1.36rem] rounded-full" :alt="item.title" />
              <div>
                <div class="text-[0.4rem] leading-[0.4rem] mt-[0.24rem]">
                  {{ item.title }}
                </div>
                <div class="text-[0.28rem] leading-[0.28rem] mt-[0.16rem]">
                  {{ item.subTitle }}
                </div>
              </div>
            </div>
          </div>
        </n-carousel-item>
        <template #arrow="{ prev, next }">
          <div class="custom-arrow">
            <icon-card
              @click="prev"
              name="fe:arrow-left"
              size="0.4rem"
              :color="pageData.currentFeedbackIndex === 0 ? '#D9D9D9' : '#e50113'"
            >
            </icon-card>
            <div class="text-[0.32rem] leading-[0.4rem] h-[0.4rem] mx-[0.2rem] mt-[0.04rem] text-[#D9D9D9]">
              {{ pageData.currentFeedbackIndex + 1 }}/{{ feedbackInfo.length }}
            </div>
            <icon-card
              @click="next"
              name="fe:arrow-right"
              size="0.4rem"
              :color="pageData.currentFeedbackIndex === feedbackInfo.length - 1 ? '#D9D9D9' : '#e50113'"
            >
            </icon-card>
          </div>
        </template>
      </n-carousel>
    </div>
    <MobileWhatsAppContact :showWhatsAppCtaBanner="false" class="mb-[0.6rem]" />
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import step1 from "@/assets/icons/home/<USER>";
import step2 from "@/assets/icons/home/<USER>";
import step3 from "@/assets/icons/home/<USER>";
import step4 from "@/assets/icons/home/<USER>";
import step5 from "@/assets/icons/home/<USER>";
import step6 from "@/assets/icons/home/<USER>";
import productQuality from "@/assets/icons/home/<USER>";
import fullConsolidation from "@/assets/icons/home/<USER>";
import translationSupport from "@/assets/icons/home/<USER>";
import ourAdvantage from "@/assets/icons/home/<USER>";
import ourProcess from "@/assets/icons/home/<USER>";
import manager1 from "@/assets/icons/home/<USER>";
import manager2 from "@/assets/icons/home/<USER>";
import manager3 from "@/assets/icons/home/<USER>";
import manager4 from "@/assets/icons/home/<USER>";
import fabian from "@/assets/icons/home/<USER>";
import gerardo from "@/assets/icons/home/<USER>";
import maria from "@/assets/icons/home/<USER>";
import fabianBg from "@/assets/icons/home/<USER>";
import gerardoBg from "@/assets/icons/home/<USER>";
import mariaBg from "@/assets/icons/home/<USER>";
import service1 from "@/assets/icons/home/<USER>";
import service2 from "@/assets/icons/home/<USER>";
import service3 from "@/assets/icons/home/<USER>";
import service4 from "@/assets/icons/home/<USER>";
import service5 from "@/assets/icons/home/<USER>";
import service6 from "@/assets/icons/home/<USER>";
import service7 from "@/assets/icons/home/<USER>";
import service8 from "@/assets/icons/home/<USER>";

useHead({
  title: "Agente de compras en China",
  link: [
    {
      rel: "canonical",
      href: `https://www.chilat.com/`,
    },
  ],
});

import { ref, onMounted, reactive } from "vue";
import type { CarouselInst } from "naive-ui";

const videoModalRef = ref<any>(null);
const feedbackCarousel = ref<CarouselInst | null>(null);

interface ServiceItem {
  icon: string;
  title: string;
  content: string;
}

const requirementInfo = [
  {
    icon: step1,
    path: "/h5/servicios/#asesoria-de-compras",
    title: "Hacer comparación en varias empresas de agencias de compras",
    content:
      "<div>¿Cuál es la agencia más confiable?</div><div>¿Quién me conviene más?</div><div>¿Quién me ayuda desarrollar?</div>",
  },
  {
    icon: step2,
    path: "/h5/servicios/#busqueda-de-proveedores-y-productos",
    title: "Encontrar productos y proveedores",
    content:
      "<div>¿Buscar fábrica directa?</div><div>¿Cómo puedo obtener los productos?</div><div>¿Ofrecer nuevos productos?</div>",
  },
  {
    icon: step3,
    path: "/h5/servicios/#viajes-de-negocios-a-china",
    title: "Asistencia en el viaje de China",
    content:
      "<div>¿Quién me ayuda con transporte y hotel?</div><div>No conozco China, necesito un guía.</div><div>No hablo chino ni inglés, ¿cómo hacerlo?</div>",
  },
  {
    icon: step4,
    path: "/h5/viajar-a-china/",
    title: "Saber cómo importar desde China",
    content:
      "<div>¿A dónde puede comprar?</div><div>¿Cómo enviar los productos a su país?</div><div>¿Cómo despachar las mercancías en aduana?</div>",
  },
  {
    icon: step5,
    path: "/h5/servicios#control-de-calidad/",
    title: "Verificar que el proveedor si sea confiable",
    content:
      "<div>¿Como sabe si un proveedor es confiable?</div><div>¿Si tiene garantía mi inversión?</div><div>¿Cómo controlar la calidad de los productos?</div>",
  },
  {
    icon: step6,
    path: "/h5/consolidacion-de-cargas-y-logistica/",
    title: "Consolidacion y despacho",
    content:
      "<div>¿Se puede hacer con marca propia?</div><div>¿Quién me ayuda a pagar a los proveedores?</div><div>¿Se pueden consolidar productos?</div>",
  },
];

const chooseInfo = [
  {
    icon: productQuality,
    path: "/h5/elijanos/#productos-y-calidad",
    title: "Productos y calidad",
    content:
      "Le ayudamos a encontrar los productos más competitivos y de más alta calidad de China, y controlamos la calidad del producto antes de despacharlo.",
  },
  {
    icon: fullConsolidation,
    path: "/h5/elijanos/#consolidaciones-y-contenedor-completo",
    title: "Consolidaciones y contenedor completo",
    content: "Juntar los productos que compra de múltiples proveedores en un mismo contenedor y despacharlos juntos.",
  },
  {
    icon: translationSupport,
    path: "/h5/elijanos/#acompanamiento-y-traduccion",
    title: "Acompañamiento y traducción",
    content:
      "Somos el puente entre los clientes y los proveedores, podemos ayudar a resolver el impedimento de comunicación, diferencias culturales, etc.",
  },
];

const serviceInfo = [
  {
    icon: service1,
    path: "/h5/servicios/#asesoria-de-compras",
    title: "Asesoría de compras",
    content: "Ofrecemos asesores para hacer negocios en china.",
  },
  {
    icon: service2,
    path: "/h5/servicios/#busqueda-de-proveedores-y-productos",
    title: "Búsqueda de proveedores y productos",
    content:
      "Buscamos y encontramos los proveedores que mejor se adaptan a sus exigencias y nos ponemos en contacto con ellos.",
  },
  {
    icon: service3,
    path: "/h5/servicios/#validacion-de-fabricantes",
    title: "Validación de fabricantes",
    content:
      "En caso de que ya cuente con un proyeedor contactado y/o precios pactados o cotizados podemos brindarle este servicio de verificación.",
  },
  {
    icon: service4,
    path: "/h5/servicios/#viajes-de-negocios-a-china",
    title: "Viajes de negocios a China",
    content: "Resolveremos todos los productos que encuanta cuando este en china.",
  },
  {
    icon: service5,
    path: "/h5/viajar-a-china/",
    title: "Agente de compras",
    content:
      "Le ayudamos a comprar directamente en el mercadode Yiwu. Somos responsables detodas las cosas que coinciden con sutrabajo.",
  },
  {
    icon: service6,
    path: "/h5/servicios/#control-de-calidad",
    title: "Control de calidad",
    content:
      "Ahorre dinero y evitar defectos costosos, garantizando que sus importaciones de productos cumplan con sus especificaciones y estándares de calidad.",
  },
  {
    icon: service7,
    path: "/h5/consolidacion-de-cargas-y-logistica/",
    title: "Consolidación de cargas y logística",
    content:
      "Consolidación de mercancias de diferentes proveedores. Coordinación y gestión del transporte de mercancias.",
  },
  {
    icon: service8,
    path: "/h5/servicios/#fotos-del-producto-y-catalogo",
    title: "Fotos del producto y catalogo",
    content: "Puede venderlas con catalogo, fotos antes de llegar sus mercancias.",
  },
];

const teamInfo = [
  {
    image: manager2,
    name: "Wang Min 汪敏 (Mindy)",
    path: "/h5/chilat-team/#Mindy",
    position: "CEO y Presidente del Grupo Chilat, Gerente General en MingZhan Trade Import & Export Co.,LTD.",
  },
  {
    image: manager1,
    name: "Renyi 任毅 (Andy)",
    path: "/h5/chilat-team/#Andy",
    position: "CEO y Presidente del Grupo Chilat, Director General MingZhan Trade Import & Export Co.,LTD.",
  },
  {
    image: manager3,
    name: "Irene",
    path: "/h5/chilat-team/#Irene",
    position: "Gerente de cliente",
  },
  {
    image: manager4,
    name: "Elisa",
    path: "/h5/chilat-team/#Elisa",
    position: "Gerente de cliente exterior de Chilat",
  },
];

const feedbackInfo = [
  {
    height: "7.52rem",
    icon: gerardo,
    bgUrl: gerardoBg,
    title: "Gerardo",
    subTitle: "Cliente de Argentina",
    content:
      "“ Primero te agradezco por el servicio que me has dado a mi empresa a la fecha ya sea por recibir en tiempo la mercadería demostrando el profesionalismo en los controles de calidad cantidad tratada acompañando en todos lo embarques la documentación requerida por las leyes en argentina . Y decirte gracias Chilat por ser eficientes y por tener un buen personal calificado y muy profesional. ”",
  },
  {
    height: "8.48rem",
    icon: maria,
    bgUrl: mariaBg,
    title: "María",
    subTitle: "Cliente de Colombia",
    content:
      "“ Hace 12 años trabajo con la empresa de la señora Mindy importó desde china para colombia muchos productos decorativos y ha sido un éxito para nu será empresa contar con un agente tan serio ,responsable y excelente manejo de dinero y nuestra carga llega a nuestro destino con toda la documentación y pedido solicitado son excelentes como compañía ampliamente los recomendamos. Suspendisse commodo ullamcorper magna egestas sem. ”",
  },
  {
    height: "5.12rem",
    icon: fabian,
    bgUrl: fabianBg,
    title: "Fabián",
    subTitle: "Clientes de Uruguay",
    content:
      "“ Agradecemos a laempresa Chilat y karmen,que nos brindaron la confianza para a hacer nuestra primer importación desde China.Gracias a la empresa@chilat por hacer un sueño realidad! ”",
  },
];

const videoInfo = [
  {
    id: "7c9B2YtloiM",
    poster: ourAdvantage,
    titleCh: "我们的优势",
    title: "Nuestra ventaja",
  },
  {
    id: "iPccIsBMF1k",
    poster: ourProcess,
    titleCh: "我们如何工作",
    title: "Cómo trabajarnos",
  },
];

const pageData = reactive({
  activeServices: [] as string[],
  imagePosition: 0,
  currentVideoIndex: 0,
  currentFeedbackIndex: 0,
  currentTeamIndex: 0,
});

onMounted(() => {
  handleServiceHover(serviceInfo[0], 0);
});

const handleServiceHover = (item: any, index: number) => {
  const title = item.title;
  const currentIndex = pageData.activeServices.indexOf(title);

  if (currentIndex === -1) {
    // 未展开则添加
    pageData.activeServices.push(title);
  } else {
    // 已展开则移除
    pageData.activeServices.splice(currentIndex, 1);
  }
};

function onOpenVideo() {
  const video = {
    id: "JYZdQqGFCeM",
    title: "Conócenos Aquí",
  };
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 13.16rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: cover;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
}

.second-carousel-wrapper {
  width: 100%;
  height: 13.16rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
}

:deep(.n-carousel.n-carousel--card .n-carousel__slide.n-carousel__slide--prev) {
  opacity: 1;
}
:deep(.n-carousel.n-carousel--card .n-carousel__slide.n-carousel__slide--next) {
  opacity: 1;
}

.service-image-enter-active,
.service-image-leave-active {
  transition: all 0.5s ease;
}

.service-image-enter-from,
.service-image-leave-to {
  opacity: 0;
}

.service-image-enter-to,
.service-image-leave-from {
  opacity: 1;
}

:deep(.service-image-move) {
  transition: transform 0.5s ease;
}

.service-item {
  border-bottom: 0.02rem solid #ebebeb;
}

// 优化过渡动画
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.n-carousel {
  overflow: visible;
}

.video-carousel {
  .custom-dots {
    display: flex;
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: -0.8rem;
    left: 0rem;
    border-radius: 0.08rem;
    background-color: #d9d9d9;
  }

  .custom-dots li {
    display: inline-block;
    width: 3.36rem;
    max-width: none;
    height: 0.02rem;
    background-color: #d9d9d9;
    border-radius: 0;
    cursor: pointer;
    margin: 0;
  }

  .custom-dots li.is-active {
    height: 0.06rem;
    background: #e50113;
  }
}

.team-carousel {
  .custom-arrow {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: -1.8rem;
    right: 0rem;
    z-index: 10;
  }
  .custom-dots {
    display: flex;
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: -1.6rem;
    left: 0rem;
    border-radius: 0.08rem;
    background-color: #d9d9d9;
  }

  .custom-dots li {
    display: inline-block;
    width: 1.12rem;
    max-width: none;
    height: 0.02rem;
    background-color: #d9d9d9;
    border-radius: 0;
    cursor: pointer;
    margin: 0;
  }

  .custom-dots li.is-active {
    height: 0.06rem;
    background: #e50113;
  }
}

.feedback-carousel {
  .custom-arrow {
    display: flex;
    position: absolute;
    bottom: 0;
    right: 0rem;
    z-index: 10;
  }
}

:deep(.homepage-carousel.n-carousel.n-carousel--bottom .n-carousel__dots) {
  bottom: 1.28rem;
  z-index: 10;
  .n-carousel__dot {
    width: 0.08rem;
    height: 0.08rem;
    border-radius: 50%;
    background-color: #333;
    opacity: 0.3;
  }
  .n-carousel__dot.n-carousel__dot--active {
    background-color: #333;
    opacity: 1;
  }
}
</style>
