<template>
  <n-layout>
    <mobile-nav-header></mobile-nav-header>
    <n-layout-content>
      <!-- 移动端页面禁止缩放 -->
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      />
      <client-only>
        <!-- 悬浮按钮 -->
        <div class="mobile-affix-middle">
          <img
            loading="lazy"
            alt="whatsapp"
            class="affix-icon whatsapp-icon"
            @click="onWhatsAppClick"
            src="@/assets/icons/common/whatsapp-icon-default.svg"
          />
        </div>
        <n-space vertical class="mobile-affix-bottom">
          <!-- 置顶按钮 -->
          <img
            loading="lazy"
            alt="返回顶部"
            class="affix-icon top-arrow-icon"
            v-if="showBackTop"
            @click="onBackTop"
            src="@/assets/icons/common/top-arrow.svg"
          />
        </n-space>
      </client-only>
      <!-- 内容 -->
      <slot />
    </n-layout-content>
    <mobile-nav-footer></mobile-nav-footer>
  </n-layout>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "mobile",
});
const showBackTop = ref(false);

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

async function onScrollBottom() {
  // 获取滚动容器已滚动的高度
  const scrollTop = window.scrollY;
  if (scrollTop > 200) {
    showBackTop.value = true;
  } else {
    showBackTop.value = false;
  }
}

function onBackTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}
</script>
<style scoped>
.n-layout {
  height: 100%;
  width: 100%;
  background: #fff;
}
.n-layout-content {
  background-color: #fff;
}
.mobile-content {
  padding-top: 0.8rem;
}
.mobile-affix-bottom {
  position: fixed;
  z-index: 1000;
  right: 0.12rem;
  bottom: 2.8rem;
  transform: translateY(50%);
}

.mobile-affix-middle {
  position: fixed;
  right: 0.12rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
}

.affix-icon {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.top-arrow-icon:hover {
  transform: scale(1.1);
}
.whatsapp-icon:hover {
  box-shadow: 0 0 0.22rem 0 rgba(0, 0, 0, 0.15);
  content: url("@/assets/icons/common/whatsapp-icon-hover.svg");
}
</style>
