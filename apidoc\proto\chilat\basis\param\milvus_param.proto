syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

message MilvusCreateCollectionParam {

  string collectionName = 10; //collection名称,类似于表名

  int32 dimension = 20; //向量维度

  string description = 30;//collection描述,类似于表的备注

}

message MilvusDropCollectionParam {
  string collectionName = 10; //collection名称,类似于表名
}

message MilvusShowCollectionsParam {
  string collectionName = 10; //collection名称,类似于表名
}

message MilvusGetAllParam {
  string collectionName = 10;
}

message MilvusDeleteByIdsParam {
  string collectionName = 10;
  repeated string ids = 20;
}

