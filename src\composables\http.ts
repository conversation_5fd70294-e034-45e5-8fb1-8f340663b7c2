/**
 *  nuxt项目目录/composables/http.ts
 */

import { useNuxtApp } from "#app";
import { useAuthStore } from "@/stores/authStore";

//定义ts变量类型接口
interface HttpParms {
  baseURL?: string; //请求的基本URL，即后台服务器地址，（若服务器请求地址只有一个，可不填）
  url: string; //请求api接口地址
  method?: any; //请求方法
  query?: any; //添加查询搜索参数到URL
  body?: any; //请求体
}

function getHttpHeaders() {
  let srcHeaders = useRequestHeaders();
  let dstHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept-Language": "es",
  };
  for (let key in srcHeaders) {
    let key2 = key.toLowerCase();
    let value = srcHeaders[key];
    if (key2 == "content-type") {
      continue;
    }
    if (key2 == "host") {
      dstHeaders["Origin-Host"] = value;
    } else {
      dstHeaders[key] = value;
    }
  }
  let visitCode = null;
  if (window && window.MyStat) {
    visitCode = window.MyStat.getVisitCode();
  } else {
    const nuxtApp = useNuxtApp();
    if (nuxtApp) {
      let globalData = nuxtApp.$getGlobalData();
      if (globalData && globalData.visitCode) {
        visitCode = globalData.visitCode;
        dstHeaders["X-Forwarded-Path"] = "/api/";
        dstHeaders["Access-Key"] = "cfd43ef903c497ad408a7902df399c33";
      }
      const originalUrl = nuxtApp.ssrContext?.url;
      if (originalUrl) {
        dstHeaders["X-Browser-Uri"] = originalUrl;
      }
    }
  }
  dstHeaders["Visit-From"] = "main";
  if (visitCode) {
    dstHeaders["Visit-Code"] = visitCode;
  }
  return dstHeaders;
}

/**
 * 网络请求方法
 * @param obj 请求参数
 * @returns 响应结果
 */
export const httpGet = (url: any, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;
  const res = new Promise<void>((resolve, reject) => {
    $fetch(`${BASEURL}` + url, {
      method: "GET",
      mode: "cors",
      cache: "no-cache",
      query: param,
      onRequest({ request, options }) {
        options.headers = getHttpHeaders();
      },
      onRequestError({ request, options, error }) {
        // 处理请求错误
        console.log("服务器链接失败!");
        reject(error);
      },
      onResponse({ request, response, options }) {
        // 处理响应数据
        resolve(response._data);
      },
      onResponseError({ request, response, options }) {
        // 处理响应错误
      },
    });
  });
  return res;
};

/**
 * 网络请求方法
 * @param obj 请求参数
 * @returns 响应结果
 */
export const httpPost = (url: any, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;

  const res = new Promise<void>((resolve, reject) => {
    $fetch(`${BASEURL}` + url, {
      method: "POST",
      mode: "cors",
      cache: "no-cache",
      body: param,
      onRequest({ request, options }) {
        // 设置请求报头
        options.headers = getHttpHeaders();
      },
      onRequestError({ request, options, error }) {
        // 处理请求错误
        console.log("服务器链接失败!");
        reject(error);
      },
      onResponse({ request, response, options }) {
        if (typeof window == "object") {
          if (response?._data?.result?.code === 403) {
            const authStore = useAuthStore();
            authStore.setUserInfo({});
          }
        }
        // 处理响应数据
        resolve(response?._data);
      },
      onResponseError({ request, response, options }) {
        // 处理响应错误
      },
    }).catch((err) => {
      reject(err);
    });
  });
  return res;
};

export const newHttpPost = async (url: string, param: any) => {
  const BASEURL =
    typeof window === "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;

  // // 记录开始时间
  // let startTime: number;

  try {
    const { data, error } = await useAsyncData(url, () =>
      $fetch(`${BASEURL}${url}`, {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        body: param,
        onRequest({ request, options }) {
          // 设置请求报头
          options.headers = getHttpHeaders();
        },
        onRequestError({ request, options, error }) {
          // 处理请求错误
          console.log("服务器链接失败!");
          throw error;
        },
        onResponse({ request, response, options }) {
          // 处理响应数据
          if (typeof window === "object") {
            if (response?._data?.result?.code === 403) {
              const authStore = useAuthStore();
              authStore.setUserInfo({});
            }
          }
          return response?._data;
        },
        onResponseError({ request, response, options }) {
          // 处理响应错误
          console.log("响应错误:", response);
          throw response._data;
        },
      })
    );

    if (error.value) {
      throw new Error(`Request failed: ${error.value.message}`);
    }

    return data.value;
  } catch (err) {
    console.error("Error in httpPost:", err);
    throw err;
  }
};

export const httpDownload = (url: string, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("POST", `${BASEURL}` + url, true);
    xhr.responseType = "blob";
    const headers = <any>{
      ...getHttpHeaders(),
      Accept: "application/json, text/plain, */*",
    };
    Object.keys(headers).forEach((header) => {
      xhr.setRequestHeader(header, headers[header]);
    });

    // 设置请求体
    if (param instanceof FormData) {
      xhr.send(param);
    } else {
      xhr.send(JSON.stringify(param));
    }

    xhr.onload = function () {
      if (xhr.status >= 200 && xhr.status < 300) {
        const data = xhr.response;
        const headers = xhr.getAllResponseHeaders();
        const result = {
          data,
          headers: headers ? parseResponseHeaders(headers) : {},
        };
        resolve(result);
      } else {
        reject();
      }
    };
    xhr.onerror = function () {
      reject();
    };
  });
};

// 响应头字符串解析为对象
function parseResponseHeaders(headersString: string) {
  const headers: Record<string, string> = {};
  if (!headersString) {
    return headers;
  }
  const lines = headersString.trim().split(/[\r\n]+/);
  lines.forEach((line) => {
    const parts = line.split(": ");
    const header = parts.shift();
    if (header) {
      headers[header] = parts.join(": ");
    }
  });
  return headers;
}

// 获取nuxt配置
export const useGetNuxtConfig = (data: any) => {
  return httpPost("/mystat/incoming", data);
};

// 获取国家
export const useGetCountry = (data: any) => {
  return httpPost("/basis/Country/listAll", data);
};

// 查询栏目
export const useListArticleCategory = (data: any) => {
  return httpPost("/pages/ArticlePage/listArticleCategory", data);
};

// 查询文章详情
export const useArticleDetail = (data: any) => {
  return newHttpPost("/pages/ArticlePage/articleDetail", data);
};

// 根据筛选条件查询文章列表
export const useSearchArticleList = (data: any) => {
  return httpPost("/pages/ArticlePage/searchArticleList", data);
};

// 保存潜客信息
export const useSaveUserInfo = (data: any) => {
  return httpPost("/marketing/PotentialUser/saveUserInfo", data);
};

// 查询360栏目
export const useListWordPressCategory = (data: any) => {
  return httpPost("/pages/WordPressPage/listWordPressCategory", data);
};

// 查询360文章详情
export const useWordPressDetail = (data: any) => {
  return httpPost("/pages/WordPressPage/wordPressDetail", data);
};

// 根据筛选条件查询360文章列表
export const useSearchWordPressList = (data: any) => {
  return httpPost("/pages/WordPressPage/searchWordPressList", data);
};

// 根据页面路径查询WhatsApp配置
export const useGetPageWhatsAppConfig = (data: any) => {
  return httpPost("/foundation/PageTool/getWhatsAppLink", data);
};
